const axios = require('axios');

async function testHeyGenEndpoints() {
  const baseUrl = 'http://172.24.175.70:5001';
  let token, sessionData;

  try {
    console.log('=== TESTING ALL HEYGEN ENDPOINTS ===\n');

    // 1. Test token endpoint
    console.log('1. Testing token endpoint...');
    const tokenResponse = await axios.post(`${baseUrl}/api/heygen/token`, {});
    console.log('✅ Token response:', tokenResponse.data);

    if (!tokenResponse.data.success || !tokenResponse.data.token) {
      throw new Error('Failed to get token');
    }

    token = tokenResponse.data.token;
    console.log('✅ Token obtained successfully\n');

    // 2. Test session creation WITHOUT voice ID
    console.log('2. Testing session creation WITHOUT voice ID...');
    try {
      const sessionResponse = await axios.post(`${baseUrl}/api/heygen/session/new`, {
        token: token,
        avatarID: 'Pedro_Chair_Sitting_public',
        voiceID: ''
      });
      console.log('✅ Session created successfully:', sessionResponse.data);
      sessionData = sessionResponse.data.sessionData;
    } catch (error) {
      console.log('❌ Session creation without voice failed:', error.response?.data);

      // 3. Try with voice ID
      console.log('\n3. Testing session creation WITH voice ID...');
      const sessionResponse = await axios.post(`${baseUrl}/api/heygen/session/new`, {
        token: token,
        avatarID: 'Pedro_Chair_Sitting_public',
        voiceID: '73c0b6a2e29d4d38aca41454bf58c955'
      });
      console.log('✅ Session created with voice:', sessionResponse.data);
      sessionData = sessionResponse.data.sessionData;
    }

    if (!sessionData) {
      throw new Error('No session data available for further testing');
    }

    // 4. Test session start
    console.log('\n4. Testing session start...');
    const startResponse = await axios.post(`${baseUrl}/api/heygen/session/start`, {
      token: token,
      sessionId: sessionData.session_id
    });
    console.log('✅ Session started:', startResponse.data);

    // 5. Test sending text to avatar
    console.log('\n5. Testing send text to avatar...');
    const taskResponse = await axios.post(`${baseUrl}/api/heygen/session/task`, {
      token: token,
      sessionId: sessionData.session_id,
      text: 'Hello, this is a test message from the backend API.',
      taskType: 'talk'
    });
    console.log('✅ Text sent to avatar:', taskResponse.data);

    // Wait a bit before stopping
    console.log('\n6. Waiting 5 seconds before stopping session...');
    await new Promise(resolve => setTimeout(resolve, 5000));

    // 6. Test session stop
    console.log('7. Testing session stop...');
    const stopResponse = await axios.post(`${baseUrl}/api/heygen/session/stop`, {
      token: token,
      sessionId: sessionData.session_id
    });
    console.log('✅ Session stopped:', stopResponse.data);

    console.log('\n🎉 ALL HEYGEN ENDPOINTS WORKING SUCCESSFULLY! 🎉');

  } catch (error) {
    console.error('\n❌ ERROR DETAILS:');
    console.error('- Message:', error.message);
    console.error('- Response status:', error.response?.status);
    console.error('- Response data:', error.response?.data);
  }
}

testHeyGenEndpoints();
