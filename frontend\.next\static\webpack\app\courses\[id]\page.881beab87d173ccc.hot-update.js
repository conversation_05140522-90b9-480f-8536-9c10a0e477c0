"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/courses/[id]/page",{

/***/ "(app-pages-browser)/./src/components/QnAWithHeyGenModal.tsx":
/*!***********************************************!*\
  !*** ./src/components/QnAWithHeyGenModal.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var livekit_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! livekit-client */ \"(app-pages-browser)/./node_modules/livekit-client/dist/livekit-client.esm.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst QnAWithHeyGenModal = (param)=>{\n    let { isOpen, onClose, courseId } = param;\n    _s();\n    // Configuration - now using backend endpoints\n    const BACKEND_URL = \"http://172.24.175.70:5001\";\n    // State variables\n    const [sessionInfo, setSessionInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [sessionToken, setSessionToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [room, setRoom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mediaStream, setMediaStream] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [webSocket, setWebSocket] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // const [avatarID, setAvatarID] = useState('Wayne_20240711');\n    const [avatarID, setAvatarID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('Pedro_Chair_Sitting_public');\n    const [voiceID, setVoiceID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [taskInput, setTaskInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [statusMessages, setStatusMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isStarted, setIsStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAutoStarting, setIsAutoStarting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Voice input states\n    const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [recognition, setRecognition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isVoiceSupported, setIsVoiceSupported] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const mediaElementRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const statusElementRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Helper function to update status with enhanced styling\n    const updateStatus = function(message) {\n        let type = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'info';\n        const timestamp = new Date().toLocaleTimeString();\n        const newMessage = {\n            message,\n            type,\n            timestamp\n        };\n        setStatusMessages((prev)=>[\n                ...prev,\n                newMessage\n            ]);\n    };\n    // Auto-scroll status to bottom\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QnAWithHeyGenModal.useEffect\": ()=>{\n            if (statusElementRef.current) {\n                statusElementRef.current.scrollTop = statusElementRef.current.scrollHeight;\n            }\n        }\n    }[\"QnAWithHeyGenModal.useEffect\"], [\n        statusMessages\n    ]);\n    // Initialize status messages and auto-start avatar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QnAWithHeyGenModal.useEffect\": ()=>{\n            if (isOpen) {\n                setStatusMessages([\n                    {\n                        message: \"Welcome to AI Avatar Assistant!\",\n                        type: \"system\",\n                        timestamp: new Date().toLocaleTimeString()\n                    },\n                    {\n                        message: \"Auto-starting avatar session...\",\n                        type: \"info\",\n                        timestamp: new Date().toLocaleTimeString()\n                    },\n                    {\n                        message: \"Please wait while we initialize your AI assistant\",\n                        type: \"info\",\n                        timestamp: new Date().toLocaleTimeString()\n                    }\n                ]);\n                // Set auto-starting state\n                setIsAutoStarting(true);\n                // Auto-start the avatar session after a brief delay to ensure component is ready\n                const autoStartTimer = setTimeout({\n                    \"QnAWithHeyGenModal.useEffect.autoStartTimer\": async ()=>{\n                        try {\n                            await handleStart();\n                            setIsAutoStarting(false);\n                        } catch (error) {\n                            console.error(\"Auto-start failed:\", error);\n                            setIsAutoStarting(false);\n                        }\n                    }\n                }[\"QnAWithHeyGenModal.useEffect.autoStartTimer\"], 1000); // 1 second delay\n                // Cleanup timer if component unmounts or modal closes\n                return ({\n                    \"QnAWithHeyGenModal.useEffect\": ()=>{\n                        clearTimeout(autoStartTimer);\n                        setIsAutoStarting(false);\n                    }\n                })[\"QnAWithHeyGenModal.useEffect\"];\n            } else {\n                // Reset states when modal closes\n                setIsAutoStarting(false);\n            }\n        }\n    }[\"QnAWithHeyGenModal.useEffect\"], [\n        isOpen\n    ]);\n    // Initialize voice recognition\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QnAWithHeyGenModal.useEffect\": ()=>{\n            if (true) {\n                // Check for Web Speech API support\n                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n                if (SpeechRecognition) {\n                    setIsVoiceSupported(true);\n                    const recognitionInstance = new SpeechRecognition();\n                    // Configure recognition\n                    recognitionInstance.continuous = false;\n                    recognitionInstance.interimResults = false;\n                    recognitionInstance.lang = 'en-US';\n                    // Handle results\n                    recognitionInstance.onresult = ({\n                        \"QnAWithHeyGenModal.useEffect\": (event)=>{\n                            const transcript = event.results[0][0].transcript;\n                            setTaskInput(transcript);\n                            updateStatus('\\uD83C\\uDFA4 Voice input: \"'.concat(transcript, '\"'), \"success\");\n                        }\n                    })[\"QnAWithHeyGenModal.useEffect\"];\n                    // Handle errors\n                    recognitionInstance.onerror = ({\n                        \"QnAWithHeyGenModal.useEffect\": (event)=>{\n                            updateStatus(\"Voice recognition error: \".concat(event.error), \"error\");\n                            setIsRecording(false);\n                        }\n                    })[\"QnAWithHeyGenModal.useEffect\"];\n                    // Handle end\n                    recognitionInstance.onend = ({\n                        \"QnAWithHeyGenModal.useEffect\": ()=>{\n                            setIsRecording(false);\n                        }\n                    })[\"QnAWithHeyGenModal.useEffect\"];\n                    setRecognition(recognitionInstance);\n                } else {\n                    setIsVoiceSupported(false);\n                    updateStatus(\"Voice input not supported in this browser\", \"warning\");\n                }\n            }\n        }\n    }[\"QnAWithHeyGenModal.useEffect\"], []);\n    // Get session token\n    const getSessionToken = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(API_CONFIG.serverUrl, \"/v1/streaming.create_token\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"X-Api-Key\": API_CONFIG.apiKey\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const data = await response.json();\n            if (!data.data || !data.data.token) {\n                throw new Error('Invalid token data received from server');\n            }\n            setSessionToken(data.data.token);\n            updateStatus(\"Session token obtained successfully\", \"success\");\n            return data.data.token;\n        } catch (error) {\n            updateStatus(\"Failed to get session token: \".concat(error.message), \"error\");\n            throw error;\n        }\n    };\n    // Connect WebSocket\n    const connectWebSocket = async (sessionId, token)=>{\n        try {\n            if (!token) {\n                throw new Error('No session token available for WebSocket connection');\n            }\n            const params = new URLSearchParams({\n                session_id: sessionId,\n                session_token: token,\n                silence_response: 'false',\n                opening_text: \"Hello, how can I help you?\",\n                stt_language: \"en\"\n            });\n            const wsUrl = \"wss://\".concat(new URL(API_CONFIG.serverUrl).hostname, \"/v1/ws/streaming.chat?\").concat(params);\n            const ws = new WebSocket(wsUrl);\n            setWebSocket(ws);\n            ws.addEventListener(\"message\", (event)=>{\n                const eventData = JSON.parse(event.data);\n                console.log(\"Raw WebSocket event:\", eventData);\n            });\n            ws.addEventListener(\"error\", (error)=>{\n                updateStatus(\"WebSocket connection error\", \"error\");\n                console.error(\"WebSocket error:\", error);\n            });\n            ws.addEventListener(\"open\", ()=>{\n                updateStatus(\"WebSocket connected successfully\", \"success\");\n            });\n        } catch (error) {\n            updateStatus(\"WebSocket connection failed: \".concat(error.message), \"error\");\n            throw error;\n        }\n    };\n    // Create new session\n    const createNewSession = async ()=>{\n        try {\n            // Always get a fresh token to avoid state timing issues\n            const token = await getSessionToken();\n            updateStatus(\"Debug: Creating session with token: \".concat(token ? 'Token exists' : 'No token'), \"info\");\n            const response = await fetch(\"\".concat(API_CONFIG.serverUrl, \"/v1/streaming.new\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    quality: \"high\",\n                    avatar_name: avatarID,\n                    voice: {\n                        voice_id: voiceID,\n                        rate: 1.0\n                    },\n                    version: \"v2\",\n                    video_encoding: \"H264\"\n                })\n            });\n            updateStatus(\"Debug: Create session response status: \".concat(response.status), \"info\");\n            if (!response.ok) {\n                const errorText = await response.text();\n                updateStatus(\"Debug: Create session error: \".concat(errorText), \"error\");\n                throw new Error(\"HTTP error! status: \".concat(response.status, \" - \").concat(errorText));\n            }\n            const data = await response.json();\n            if (!data.data || !data.data.session_id) {\n                throw new Error('Invalid session data received from server');\n            }\n            updateStatus(\"Debug: Session data keys: \".concat(Object.keys(data.data).join(', ')), \"info\");\n            // Set session info and return it for immediate use\n            setSessionInfo(data.data);\n            // Create LiveKit Room\n            const livekitRoom = new livekit_client__WEBPACK_IMPORTED_MODULE_2__.Room({\n                adaptiveStream: true,\n                dynacast: true,\n                videoCaptureDefaults: {\n                    resolution: livekit_client__WEBPACK_IMPORTED_MODULE_2__.VideoPresets.h720.resolution\n                }\n            });\n            setRoom(livekitRoom);\n            // Handle room events\n            livekitRoom.on(livekit_client__WEBPACK_IMPORTED_MODULE_2__.RoomEvent.DataReceived, (message)=>{\n                const data = new TextDecoder().decode(message);\n                console.log(\"Room message:\", JSON.parse(data));\n            });\n            // Handle media streams\n            const newMediaStream = new MediaStream();\n            setMediaStream(newMediaStream);\n            livekitRoom.on(livekit_client__WEBPACK_IMPORTED_MODULE_2__.RoomEvent.TrackSubscribed, (track)=>{\n                if (track.kind === \"video\" || track.kind === \"audio\") {\n                    newMediaStream.addTrack(track.mediaStreamTrack);\n                    if (newMediaStream.getVideoTracks().length > 0 && newMediaStream.getAudioTracks().length > 0) {\n                        if (mediaElementRef.current) {\n                            mediaElementRef.current.srcObject = newMediaStream;\n                            updateStatus(\"Media stream ready - Avatar is live!\", \"success\");\n                        }\n                    }\n                }\n            });\n            // Handle media stream removal\n            livekitRoom.on(livekit_client__WEBPACK_IMPORTED_MODULE_2__.RoomEvent.TrackUnsubscribed, (track)=>{\n                const mediaTrack = track.mediaStreamTrack;\n                if (mediaTrack) {\n                    newMediaStream.removeTrack(mediaTrack);\n                }\n            });\n            // Handle room connection state changes\n            livekitRoom.on(livekit_client__WEBPACK_IMPORTED_MODULE_2__.RoomEvent.Disconnected, (reason)=>{\n                updateStatus(\"Room disconnected: \".concat(reason), \"warning\");\n            });\n            await livekitRoom.prepareConnection(data.data.url, data.data.access_token);\n            updateStatus(\"Connection prepared successfully\", \"success\");\n            await connectWebSocket(data.data.session_id, token);\n            updateStatus(\"Session created successfully\", \"success\");\n            // Return both session data, token, and room for immediate use\n            return {\n                sessionData: data.data,\n                token,\n                room: livekitRoom\n            };\n        } catch (error) {\n            updateStatus(\"Failed to create session: \".concat(error.message), \"error\");\n            throw error;\n        }\n    };\n    // Start streaming session\n    const startStreamingSession = async (sessionData, token, livekitRoom)=>{\n        if (!sessionData || !sessionData.session_id) {\n            throw new Error('No session info available');\n        }\n        try {\n            updateStatus(\"Debug: Using token: \".concat(token ? 'Token exists' : 'No token'), \"info\");\n            updateStatus(\"Debug: Session ID: \".concat(sessionData.session_id), \"info\");\n            const startResponse = await fetch(\"\".concat(API_CONFIG.serverUrl, \"/v1/streaming.start\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    session_id: sessionData.session_id\n                })\n            });\n            updateStatus(\"Debug: Start response status: \".concat(startResponse.status), \"info\");\n            if (!startResponse.ok) {\n                const errorText = await startResponse.text();\n                updateStatus(\"Debug: Error response: \".concat(errorText), \"error\");\n                throw new Error(\"HTTP error! status: \".concat(startResponse.status, \" - \").concat(errorText));\n            }\n            // Connect to LiveKit room\n            updateStatus(\"Debug: Available session properties: \".concat(Object.keys(sessionData).join(', ')), \"info\");\n            updateStatus(\"Debug: Room exists: \".concat(!!livekitRoom), \"info\");\n            updateStatus(\"Debug: URL exists: \".concat(!!sessionData.url), \"info\");\n            updateStatus(\"Debug: Access token exists: \".concat(!!sessionData.access_token), \"info\");\n            if (livekitRoom && sessionData.url && sessionData.access_token) {\n                await livekitRoom.connect(sessionData.url, sessionData.access_token);\n                updateStatus(\"Connected to room successfully\", \"success\");\n            } else {\n                updateStatus(\"Warning: Room or connection details missing\", \"warning\");\n                updateStatus(\"Debug: Missing - Room: \".concat(!livekitRoom, \", URL: \").concat(!sessionData.url, \", Token: \").concat(!sessionData.access_token), \"error\");\n            }\n            setIsStarted(true);\n            updateStatus(\"Streaming started successfully\", \"success\");\n        } catch (error) {\n            updateStatus(\"Failed to start streaming: \".concat(error.message), \"error\");\n            throw error;\n        }\n    };\n    // Send text to avatar\n    const sendText = async function(text) {\n        let taskType = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"talk\";\n        if (!sessionInfo) {\n            updateStatus(\"No active session - please start the avatar first\", \"warning\");\n            return;\n        }\n        const token = sessionToken;\n        if (!token) {\n            updateStatus(\"No session token available\", \"error\");\n            return;\n        }\n        try {\n            updateStatus(\"Sending message to avatar...\", \"info\");\n            const response = await fetch(\"\".concat(API_CONFIG.serverUrl, \"/v1/streaming.task\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    session_id: sessionInfo.session_id,\n                    text: text,\n                    task_type: taskType\n                })\n            });\n            const data = await response.json();\n            if (!response.ok || data.error) {\n                updateStatus(\"HeyGen API error: \".concat(data.error || response.statusText), \"error\");\n            } else {\n                updateStatus(\"✓ Message sent successfully (\".concat(taskType, \")\"), \"success\");\n                updateStatus('Avatar will speak: \"'.concat(text.substring(0, 50)).concat(text.length > 50 ? '...' : '', '\"'), \"info\");\n            }\n        } catch (err) {\n            updateStatus(\"HeyGen API call failed: \" + err.message, \"error\");\n        }\n    };\n    // Close session\n    const closeSession = async ()=>{\n        if (!sessionInfo) {\n            updateStatus(\"No active session\", \"warning\");\n            return;\n        }\n        try {\n            const token = sessionToken;\n            if (token) {\n                const response = await fetch(\"\".concat(API_CONFIG.serverUrl, \"/v1/streaming.stop\"), {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        Authorization: \"Bearer \".concat(token)\n                    },\n                    body: JSON.stringify({\n                        session_id: sessionInfo.session_id\n                    })\n                });\n            }\n            // Close WebSocket\n            if (webSocket) {\n                webSocket.close();\n            }\n            // Disconnect from LiveKit room\n            if (room) {\n                room.disconnect();\n            }\n            // Reset states\n            if (mediaElementRef.current) {\n                mediaElementRef.current.srcObject = null;\n            }\n            setSessionInfo(null);\n            setRoom(null);\n            setMediaStream(null);\n            setSessionToken(null);\n            setIsStarted(false);\n            updateStatus(\"Session closed successfully\", \"warning\");\n        } catch (error) {\n            updateStatus(\"Error closing session: \".concat(error.message), \"error\");\n            // Still reset states even if API call fails\n            setSessionInfo(null);\n            setRoom(null);\n            setMediaStream(null);\n            setSessionToken(null);\n            setIsStarted(false);\n        }\n    };\n    // Azure Search and OpenAI integration\n    const callAzureOpenAI = async (prompt)=>{\n        try {\n            var _data_choices__message, _data_choices_, _data_choices;\n            const response = await fetch(\"http://172.24.175.70:5001\" + \"/api/openai\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    prompt\n                })\n            });\n            const data = await response.json();\n            if (!response.ok || !data.choices) {\n                const errorMsg = data.error ? typeof data.error === 'object' ? JSON.stringify(data.error) : data.error : response.statusText;\n                updateStatus(\"Azure OpenAI error: \" + errorMsg, \"error\");\n                return null;\n            }\n            return (_data_choices = data.choices) === null || _data_choices === void 0 ? void 0 : (_data_choices_ = _data_choices[0]) === null || _data_choices_ === void 0 ? void 0 : (_data_choices__message = _data_choices_.message) === null || _data_choices__message === void 0 ? void 0 : _data_choices__message.content;\n        } catch (err) {\n            updateStatus(\"Azure OpenAI call failed: \" + err.message, \"error\");\n            return null;\n        }\n    };\n    const searchAzure = async (query)=>{\n        try {\n            const response = await fetch(\"http://172.24.175.70:5001\" + \"/api/search\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    query,\n                    courseId\n                })\n            });\n            const data = await response.json();\n            if (!response.ok || !data.value) {\n                const errorMsg = data.error ? typeof data.error === 'object' ? JSON.stringify(data.error) : data.error : response.statusText;\n                updateStatus(\"Azure Search error: \" + errorMsg, \"error\");\n                return null;\n            }\n            updateStatus(\"Found \".concat(data.value.length, \" relevant documents\"), \"success\");\n            return (data.value || []).map((r)=>r.content).join('\\n\\n');\n        } catch (err) {\n            updateStatus(\"Azure Search call failed: \" + err.message, \"error\");\n            return null;\n        }\n    };\n    // Handle Ask AI button\n    const handleAskAI = async ()=>{\n        const text = taskInput.trim();\n        if (!text) {\n            updateStatus(\"Please enter a question first\", \"warning\");\n            return;\n        }\n        if (!sessionInfo) {\n            updateStatus(\"Please start the avatar session first\", \"warning\");\n            return;\n        }\n        try {\n            updateStatus('\\uD83D\\uDD0D Searching for: \"'.concat(text, '\"'), \"info\");\n            const searchResults = await searchAzure(text);\n            if (!searchResults) {\n                updateStatus(\"No relevant documents found for your question\", \"warning\");\n                return;\n            }\n            updateStatus(\"🤖 Processing with AI...\", \"info\");\n            const llmAnswer = await callAzureOpenAI('Based on the following context, answer the question: \"'.concat(text, '\"\\n\\nContext: ').concat(searchResults));\n            if (llmAnswer) {\n                updateStatus(\"\\uD83D\\uDCA1 AI Response: \".concat(llmAnswer.substring(0, 100)).concat(llmAnswer.length > 100 ? '...' : ''), \"success\");\n                // Clean the response for avatar speech\n                let cleaned = llmAnswer.replace(/[#*_`>-]+/g, '').replace(/\\n{2,}/g, ' ').replace(/\\s{2,}/g, ' ').trim();\n                updateStatus(\"\\uD83C\\uDFA4 Avatar speaking response...\", \"info\");\n                await sendText(cleaned, \"repeat\");\n                setTaskInput(\"\");\n            } else {\n                updateStatus(\"Failed to generate AI response\", \"error\");\n            }\n        } catch (error) {\n            updateStatus(\"Error processing question: \".concat(error.message), \"error\");\n        }\n    };\n    // Handle Voice Input button\n    const handleVoiceInput = ()=>{\n        if (!isVoiceSupported) {\n            updateStatus(\"Voice input not supported in this browser\", \"warning\");\n            return;\n        }\n        if (!recognition) {\n            updateStatus(\"Voice recognition not initialized\", \"error\");\n            return;\n        }\n        if (isRecording) {\n            // Stop recording\n            recognition.stop();\n            setIsRecording(false);\n            updateStatus(\"Voice recording stopped\", \"info\");\n        } else {\n            // Start recording\n            try {\n                recognition.start();\n                setIsRecording(true);\n                updateStatus(\"🎤 Listening... Speak your question\", \"info\");\n            } catch (error) {\n                updateStatus(\"Failed to start voice recording: \".concat(error.message), \"error\");\n                setIsRecording(false);\n            }\n        }\n    };\n    // Handle Start button\n    const handleStart = async ()=>{\n        try {\n            const result = await createNewSession();\n            await startStreamingSession(result.sessionData, result.token, result.room);\n        } catch (error) {\n            updateStatus(\"Error starting session: \".concat(error.message), \"error\");\n        }\n    };\n    // Handle Enter key in textarea\n    const handleKeyDown = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleAskAI();\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-2xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-white bg-opacity-20 rounded-xl flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-white\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 604,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                            lineNumber: 603,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                        lineNumber: 602,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-bold\",\n                                                children: \"AI Avatar Assistant\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 608,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white text-opacity-70 text-sm\",\n                                                children: \"Powered by EXL\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                        lineNumber: 607,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                lineNumber: 601,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3 h-3 rounded-full \".concat(isStarted ? 'bg-green-400 animate-pulse' : 'bg-gray-400')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 614,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-opacity-80 text-sm font-medium\",\n                                                children: isStarted ? 'Live' : 'Offline'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 615,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                        lineNumber: 613,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: onClose,\n                                        className: \"text-white hover:text-gray-200 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M6 18L18 6M6 6l12 12\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 624,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                            lineNumber: 623,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                        lineNumber: 619,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                lineNumber: 612,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                        lineNumber: 600,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                    lineNumber: 599,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 max-h-[calc(90vh-120px)] overflow-y-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-5 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 rounded-2xl p-6 border border-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-800 mb-4 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5 mr-2 text-cyan-600\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                        lineNumber: 643,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                    lineNumber: 642,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Ask Your Question\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                            lineNumber: 641,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        placeholder: \"Type your question here...\",\n                                                        value: taskInput,\n                                                        onChange: (e)=>setTaskInput(e.target.value),\n                                                        onKeyDown: handleKeyDown,\n                                                        className: \"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all duration-200 resize-none h-24\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                        lineNumber: 650,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                    lineNumber: 649,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleAskAI,\n                                                            className: \"px-4 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4 inline mr-2\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                        lineNumber: 665,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                    lineNumber: 664,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                \"Ask AI\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                            lineNumber: 660,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleVoiceInput,\n                                                            disabled: !isVoiceSupported,\n                                                            className: \"px-4 py-3 text-white rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg \".concat(isRecording ? 'bg-red-500 hover:bg-red-600 animate-pulse' : isVoiceSupported ? 'bg-purple-500 hover:bg-purple-600' : 'bg-gray-400 cursor-not-allowed'),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4 inline mr-2\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: isRecording ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a2 2 0 114 0v4a2 2 0 11-4 0V7z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                        lineNumber: 682,\n                                                                        columnNumber: 27\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                        lineNumber: 684,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                    lineNumber: 680,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                isRecording ? 'Stop Recording' : 'Voice Input'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                            lineNumber: 669,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                    lineNumber: 659,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                            lineNumber: 648,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                    lineNumber: 640,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                lineNumber: 636,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 rounded-2xl p-6 border border-gray-200 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-lg font-semibold text-gray-800 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5 mr-2 text-blue-600\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M2 6a2 2 0 012-2h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 701,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                            lineNumber: 700,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \"AI Avatar\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                    lineNumber: 699,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 rounded-full \".concat(isAutoStarting ? 'bg-yellow-500 animate-pulse' : isStarted ? 'bg-green-500 animate-pulse' : 'bg-gray-400')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                            lineNumber: 706,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: isAutoStarting ? 'Starting...' : isStarted ? 'Connected' : 'Ready'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                            lineNumber: 713,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                    lineNumber: 705,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                            lineNumber: 698,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                ref: mediaElementRef,\n                                                className: \"w-full h-96 bg-gray-900 rounded-xl object-cover shadow-lg\",\n                                                autoPlay: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center text-white\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-16 h-16 mx-auto mb-4 opacity-50\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                    lineNumber: 728,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 727,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-lg font-medium\",\n                                                                children: \"Avatar will appear here\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 730,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm opacity-75\",\n                                                                children: \"Auto-starting avatar session...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 731,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                        lineNumber: 726,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                    lineNumber: 725,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 720,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                            lineNumber: 719,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                    lineNumber: 697,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                lineNumber: 695,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                        lineNumber: 633,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                    lineNumber: 632,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n            lineNumber: 597,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n        lineNumber: 596,\n        columnNumber: 5\n    }, undefined);\n};\n_s(QnAWithHeyGenModal, \"KD4zp0K+Vr4yX6MzZrH14V/PMpU=\");\n_c = QnAWithHeyGenModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (QnAWithHeyGenModal);\nvar _c;\n$RefreshReg$(_c, \"QnAWithHeyGenModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/QnAWithHeyGenModal.tsx\n"));

/***/ })

});