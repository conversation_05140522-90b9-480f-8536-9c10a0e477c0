(()=>{var e={};e.id=105,e.ids=[105],e.modules={4:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\Navbar.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\AI-Trainer3\\frontend\\src\\components\\Navbar.tsx","default")},440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(1658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1797:(e,t,r)=>{"use strict";var s=r(4098);function n(){}function a(){}a.resetWarningCache=n,e.exports=function(){function e(e,t,r,n,a,o){if(o!==s){var i=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw i.name="Invariant Violation",i}}function t(){return e}e.isRequired=e;var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:a,resetWarningCache:n};return r.PropTypes=r,r}},1820:e=>{"use strict";e.exports=require("os")},2977:(e,t,r)=>{Promise.resolve().then(r.bind(r,9190))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},3997:e=>{"use strict";e.exports=require("tty")},4075:e=>{"use strict";e.exports=require("zlib")},4098:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},6395:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>u,tree:()=>l});var s=r(5239),n=r(8088),a=r(8170),o=r.n(a),i=r(893),c={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);r.d(t,c);let l={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,8754)),"C:\\Projects\\AI-Trainer3\\frontend\\src\\app\\dashboard\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,8429)),"C:\\Projects\\AI-Trainer3\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Projects\\AI-Trainer3\\frontend\\src\\app\\dashboard\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},6529:(e,t,r)=>{Promise.resolve().then(r.bind(r,4))},7910:e=>{"use strict";e.exports=require("stream")},8209:(e,t,r)=>{e.exports=r(1797)()},8354:e=>{"use strict";e.exports=require("util")},8754:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>T});var s=r(7413),n=r(4),a=r(1120),o=r.n(a),i=r(5529),c=r(8209),l=r.n(c);function d(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,s)}return r}function p(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?d(Object(r),!0).forEach(function(t){f(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function u(e){return(u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function f(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function m(e){return function(e){if(Array.isArray(e))return x(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return x(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return x(e,t)}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function x(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,s=Array(t);r<t;r++)s[r]=e[r];return s}function b(e){var t;return(t=e-0)==t?e:(e=e.replace(/[\-_\s]+(.)?/g,function(e,t){return t?t.toUpperCase():""})).substr(0,1).toLowerCase()+e.substr(1)}var h=["style"],g=!1;try{g=!0}catch(e){}function y(e){return e&&"object"===u(e)&&e.prefix&&e.iconName&&e.icon?e:i.qg.icon?i.qg.icon(e):null===e?null:e&&"object"===u(e)&&e.prefix&&e.iconName?e:Array.isArray(e)&&2===e.length?{prefix:e[0],iconName:e[1]}:"string"==typeof e?{prefix:"fas",iconName:e}:void 0}function v(e,t){return Array.isArray(t)&&t.length>0||!Array.isArray(t)&&t?f({},e,t):{}}var j={border:!1,className:"",mask:null,maskId:null,fixedWidth:!1,inverse:!1,flip:!1,icon:null,listItem:!1,pull:null,pulse:!1,rotation:null,size:null,spin:!1,spinPulse:!1,spinReverse:!1,beat:!1,fade:!1,beatFade:!1,bounce:!1,shake:!1,symbol:!1,title:"",titleId:null,transform:null,swapOpacity:!1},N=o().forwardRef(function(e,t){var r,s,n,a,o,c,l,d,u,x,b,h,N,O,C,P,z,k,L,S,_=p(p({},j),e),I=_.icon,A=_.mask,M=_.symbol,q=_.className,T=_.title,E=_.titleId,D=_.maskId,R=y(I),W=v("classes",[].concat(m((s=_.beat,n=_.fade,a=_.beatFade,o=_.bounce,c=_.shake,l=_.flash,d=_.spin,u=_.spinPulse,x=_.spinReverse,b=_.pulse,h=_.fixedWidth,N=_.inverse,O=_.border,C=_.listItem,P=_.flip,z=_.size,k=_.rotation,L=_.pull,Object.keys((f(r={"fa-beat":s,"fa-fade":n,"fa-beat-fade":a,"fa-bounce":o,"fa-shake":c,"fa-flash":l,"fa-spin":d,"fa-spin-reverse":x,"fa-spin-pulse":u,"fa-pulse":b,"fa-fw":h,"fa-inverse":N,"fa-border":O,"fa-li":C,"fa-flip":!0===P,"fa-flip-horizontal":"horizontal"===P||"both"===P,"fa-flip-vertical":"vertical"===P||"both"===P},"fa-".concat(z),null!=z),f(r,"fa-rotate-".concat(k),null!=k&&0!==k),f(r,"fa-pull-".concat(L),null!=L),f(r,"fa-swap-opacity",_.swapOpacity),S=r)).map(function(e){return S[e]?e:null}).filter(function(e){return e}))),m((q||"").split(" ")))),U=v("transform","string"==typeof _.transform?i.qg.transform(_.transform):_.transform),G=v("mask",y(A)),F=(0,i.Kk)(R,p(p(p(p({},W),U),G),{},{symbol:M,title:T,titleId:E,maskId:D}));if(!F)return!function(){if(!g&&console&&"function"==typeof console.error){var e;(e=console).error.apply(e,arguments)}}("Could not find icon",R),null;var X=F.abstract,J={ref:t};return Object.keys(_).forEach(function(e){j.hasOwnProperty(e)||(J[e]=_[e])}),w(X[0],J)});N.displayName="FontAwesomeIcon",N.propTypes={beat:l().bool,border:l().bool,beatFade:l().bool,bounce:l().bool,className:l().string,fade:l().bool,flash:l().bool,mask:l().oneOfType([l().object,l().array,l().string]),maskId:l().string,fixedWidth:l().bool,inverse:l().bool,flip:l().oneOf([!0,!1,"horizontal","vertical","both"]),icon:l().oneOfType([l().object,l().array,l().string]),listItem:l().bool,pull:l().oneOf(["right","left"]),pulse:l().bool,rotation:l().oneOf([0,90,180,270]),shake:l().bool,size:l().oneOf(["2xs","xs","sm","lg","xl","2xl","1x","2x","3x","4x","5x","6x","7x","8x","9x","10x"]),spin:l().bool,spinPulse:l().bool,spinReverse:l().bool,symbol:l().oneOfType([l().bool,l().string]),title:l().string,titleId:l().string,transform:l().oneOfType([l().string,l().object]),swapOpacity:l().bool};var w=(function e(t,r){var s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if("string"==typeof r)return r;var n=(r.children||[]).map(function(r){return e(t,r)}),a=Object.keys(r.attributes||{}).reduce(function(e,t){var s=r.attributes[t];switch(t){case"class":e.attrs.className=s,delete r.attributes.class;break;case"style":e.attrs.style=s.split(";").map(function(e){return e.trim()}).filter(function(e){return e}).reduce(function(e,t){var r=t.indexOf(":"),s=b(t.slice(0,r)),n=t.slice(r+1).trim();return s.startsWith("webkit")?e[s.charAt(0).toUpperCase()+s.slice(1)]=n:e[s]=n,e},{});break;default:0===t.indexOf("aria-")||0===t.indexOf("data-")?e.attrs[t.toLowerCase()]=s:e.attrs[b(t)]=s}return e},{attrs:{}}),o=s.style,i=function(e,t){if(null==e)return{};var r,s,n=function(e,t){if(null==e)return{};var r,s,n={},a=Object.keys(e);for(s=0;s<a.length;s++)r=a[s],t.indexOf(r)>=0||(n[r]=e[r]);return n}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(s=0;s<a.length;s++)r=a[s],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}(s,h);return a.attrs.style=p(p({},a.attrs.style),void 0===o?{}:o),t.apply(void 0,[r.tag,p(p({},a.attrs),i)].concat(m(n)))}).bind(null,o().createElement);let O=({title:e,description:t,icon:r})=>(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 flex flex-col items-center text-center transform transition duration-300 hover:scale-105 hover:shadow-lg cursor-pointer",children:[(0,s.jsx)("div",{className:"text-orange-500 text-4xl mb-4",children:(0,s.jsx)(N,{icon:r})}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-800 mb-2",children:e}),(0,s.jsx)("p",{className:"text-gray-600 text-sm",children:t})]}),C={prefix:"fas",iconName:"cloud",icon:[640,512,[9729],"f0c2","M0 336c0 79.5 64.5 144 144 144l368 0c70.7 0 128-57.3 128-128c0-61.9-44-113.6-102.4-125.4c4.1-10.7 6.4-22.4 6.4-34.6c0-53-43-96-96-96c-19.7 0-38.1 6-53.3 16.2C367 64.2 315.3 32 256 32C167.6 32 96 103.6 96 192c0 2.7 .1 5.4 .2 8.1C40.2 219.8 0 273.2 0 336z"]},P={prefix:"fas",iconName:"lightbulb",icon:[384,512,[128161],"f0eb","M272 384c9.6-31.9 29.5-59.1 49.2-86.2c0 0 0 0 0 0c5.2-7.1 10.4-14.2 15.4-21.4c19.8-28.5 31.4-63 31.4-100.3C368 78.8 289.2 0 192 0S16 78.8 16 176c0 37.3 11.6 71.9 31.4 100.3c5 7.2 10.2 14.3 15.4 21.4c0 0 0 0 0 0c19.8 27.1 39.7 54.4 49.2 86.2l160 0zM192 512c44.2 0 80-35.8 80-80l0-16-160 0 0 16c0 44.2 35.8 80 80 80zM112 176c0 8.8-7.2 16-16 16s-16-7.2-16-16c0-61.9 50.1-112 112-112c8.8 0 16 7.2 16 16s-7.2 16-16 16c-44.2 0-80 35.8-80 80z"]},z={prefix:"fas",iconName:"shield-halved",icon:[512,512,["shield-alt"],"f3ed","M256 0c4.6 0 9.2 1 13.4 2.9L457.7 82.8c22 9.3 38.4 31 38.3 57.2c-.5 99.2-41.3 280.7-213.6 363.2c-16.7 8-36.1 8-52.8 0C57.3 420.7 16.5 239.2 16 140c-.1-26.2 16.3-47.9 38.3-57.2L242.7 2.9C246.8 1 251.4 0 256 0zm0 66.8l0 378.1C394 378 431.1 230.1 432 141.4L256 66.8s0 0 0 0z"]},k={prefix:"fas",iconName:"code",icon:[640,512,[],"f121","M392.8 1.2c-17-4.9-34.7 5-39.6 22l-128 448c-4.9 17 5 34.7 22 39.6s34.7-5 39.6-22l128-448c4.9-17-5-34.7-22-39.6zm80.6 120.1c-12.5 12.5-12.5 32.8 0 45.3L562.7 256l-89.4 89.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0l112-112c12.5-12.5 12.5-32.8 0-45.3l-112-112c-12.5-12.5-32.8-12.5-45.3 0zm-306.7 0c-12.5-12.5-32.8-12.5-45.3 0l-112 112c-12.5 12.5-12.5 32.8 0 45.3l112 112c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L77.3 256l89.4-89.4c12.5-12.5 12.5-32.8 0-45.3z"]},L={prefix:"fas",iconName:"chart-line",icon:[512,512,["line-chart"],"f201","M64 64c0-17.7-14.3-32-32-32S0 46.3 0 64L0 400c0 44.2 35.8 80 80 80l400 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L80 416c-8.8 0-16-7.2-16-16L64 64zm406.6 86.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L320 210.7l-57.4-57.4c-12.5-12.5-32.8-12.5-45.3 0l-112 112c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L240 221.3l57.4 57.4c12.5 12.5 32.8 12.5 45.3 0l128-128z"]},S={prefix:"fas",iconName:"mobile-screen-button",icon:[384,512,["mobile-alt"],"f3cd","M16 64C16 28.7 44.7 0 80 0L304 0c35.3 0 64 28.7 64 64l0 384c0 35.3-28.7 64-64 64L80 512c-35.3 0-64-28.7-64-64L16 64zM224 448a32 32 0 1 0 -64 0 32 32 0 1 0 64 0zM304 64L80 64l0 320 224 0 0-320z"]},_={prefix:"fas",iconName:"diagram-project",icon:[576,512,["project-diagram"],"f542","M0 80C0 53.5 21.5 32 48 32l96 0c26.5 0 48 21.5 48 48l0 16 192 0 0-16c0-26.5 21.5-48 48-48l96 0c26.5 0 48 21.5 48 48l0 96c0 26.5-21.5 48-48 48l-96 0c-26.5 0-48-21.5-48-48l0-16-192 0 0 16c0 1.7-.1 3.4-.3 5L272 288l96 0c26.5 0 48 21.5 48 48l0 96c0 26.5-21.5 48-48 48l-96 0c-26.5 0-48-21.5-48-48l0-96c0-1.7 .1-3.4 .3-5L144 224l-96 0c-26.5 0-48-21.5-48-48L0 80z"]},I={prefix:"fas",iconName:"robot",icon:[640,512,[129302],"f544","M320 0c17.7 0 32 14.3 32 32l0 64 120 0c39.8 0 72 32.2 72 72l0 272c0 39.8-32.2 72-72 72l-304 0c-39.8 0-72-32.2-72-72l0-272c0-39.8 32.2-72 72-72l120 0 0-64c0-17.7 14.3-32 32-32zM208 384c-8.8 0-16 7.2-16 16s7.2 16 16 16l32 0c8.8 0 16-7.2 16-16s-7.2-16-16-16l-32 0zm96 0c-8.8 0-16 7.2-16 16s7.2 16 16 16l32 0c8.8 0 16-7.2 16-16s-7.2-16-16-16l-32 0zm96 0c-8.8 0-16 7.2-16 16s7.2 16 16 16l32 0c8.8 0 16-7.2 16-16s-7.2-16-16-16l-32 0zM264 256a40 40 0 1 0 -80 0 40 40 0 1 0 80 0zm152 40a40 40 0 1 0 0-80 40 40 0 1 0 0 80zM48 224l16 0 0 192-16 0c-26.5 0-48-21.5-48-48l0-96c0-26.5 21.5-48 48-48zm544 0c26.5 0 48 21.5 48 48l0 96c0 26.5-21.5 48-48 48l-16 0 0-192 16 0z"]},A={prefix:"fas",iconName:"gears",icon:[640,512,["cogs"],"f085","M308.5 135.3c7.1-6.3 9.9-16.2 6.2-25c-2.3-5.3-4.8-10.5-7.6-15.5L304 89.4c-3-5-6.3-9.9-9.8-14.6c-5.7-7.6-15.7-10.1-24.7-7.1l-28.2 9.3c-10.7-8.8-23-16-36.2-20.9L199 27.1c-1.9-9.3-9.1-16.7-18.5-17.8C173.9 8.4 167.2 8 160.4 8l-.7 0c-6.8 0-13.5 .4-20.1 1.2c-9.4 1.1-16.6 8.6-18.5 17.8L115 56.1c-13.3 5-25.5 12.1-36.2 20.9L50.5 67.8c-9-3-19-.5-24.7 7.1c-3.5 4.7-6.8 9.6-9.9 14.6l-3 5.3c-2.8 5-5.3 10.2-7.6 15.6c-3.7 8.7-.9 18.6 6.2 25l22.2 19.8C32.6 161.9 32 168.9 32 176s.6 14.1 1.7 20.9L11.5 216.7c-7.1 6.3-9.9 16.2-6.2 25c2.3 5.3 4.8 10.5 7.6 15.6l3 5.2c3 5.1 6.3 9.9 9.9 14.6c5.7 7.6 15.7 10.1 24.7 7.1l28.2-9.3c10.7 8.8 23 16 36.2 20.9l6.1 29.1c1.9 9.3 9.1 16.7 18.5 17.8c6.7 .8 13.5 1.2 20.4 1.2s13.7-.4 20.4-1.2c9.4-1.1 16.6-8.6 18.5-17.8l6.1-29.1c13.3-5 25.5-12.1 36.2-20.9l28.2 9.3c9 3 19 .5 24.7-7.1c3.5-4.7 6.8-9.5 9.8-14.6l3.1-5.4c2.8-5 5.3-10.2 7.6-15.5c3.7-8.7 .9-18.6-6.2-25l-22.2-19.8c1.1-6.8 1.7-13.8 1.7-20.9s-.6-14.1-1.7-20.9l22.2-19.8zM112 176a48 48 0 1 1 96 0 48 48 0 1 1 -96 0zM504.7 500.5c6.3 7.1 16.2 9.9 25 6.2c5.3-2.3 10.5-4.8 15.5-7.6l5.4-3.1c5-3 9.9-6.3 14.6-9.8c7.6-5.7 10.1-15.7 7.1-24.7l-9.3-28.2c8.8-10.7 16-23 20.9-36.2l29.1-6.1c9.3-1.9 16.7-9.1 17.8-18.5c.8-6.7 1.2-13.5 1.2-20.4s-.4-13.7-1.2-20.4c-1.1-9.4-8.6-16.6-17.8-18.5L583.9 307c-5-13.3-12.1-25.5-20.9-36.2l9.3-28.2c3-9 .5-19-7.1-24.7c-4.7-3.5-9.6-6.8-14.6-9.9l-5.3-3c-5-2.8-10.2-5.3-15.6-7.6c-8.7-3.7-18.6-.9-25 6.2l-19.8 22.2c-6.8-1.1-13.8-1.7-20.9-1.7s-14.1 .6-20.9 1.7l-19.8-22.2c-6.3-7.1-16.2-9.9-25-6.2c-5.3 2.3-10.5 4.8-15.6 7.6l-5.2 3c-5.1 3-9.9 6.3-14.6 9.9c-7.6 5.7-10.1 15.7-7.1 24.7l9.3 28.2c-8.8 10.7-16 23-20.9 36.2L315.1 313c-9.3 1.9-16.7 9.1-17.8 18.5c-.8 6.7-1.2 13.5-1.2 20.4s.4 13.7 1.2 20.4c1.1 9.4 8.6 16.6 17.8 18.5l29.1 6.1c5 13.3 12.1 25.5 20.9 36.2l-9.3 28.2c-3 9-.5 19 7.1 24.7c4.7 3.5 9.5 6.8 14.6 9.8l5.4 3.1c5 2.8 10.2 5.3 15.5 7.6c8.7 3.7 18.6 .9 25-6.2l19.8-22.2c6.8 1.1 13.8 1.7 20.9 1.7s14.1-.6 20.9-1.7l19.8 22.2zM464 304a48 48 0 1 1 0 96 48 48 0 1 1 0-96z"]},M={prefix:"fas",iconName:"database",icon:[448,512,[],"f1c0","M448 80l0 48c0 44.2-100.3 80-224 80S0 172.2 0 128L0 80C0 35.8 100.3 0 224 0S448 35.8 448 80zM393.2 214.7c20.8-7.4 39.9-16.9 54.8-28.6L448 288c0 44.2-100.3 80-224 80S0 332.2 0 288L0 186.1c14.9 11.8 34 21.2 54.8 28.6C99.7 230.7 159.5 240 224 240s124.3-9.3 169.2-25.3zM0 346.1c14.9 11.8 34 21.2 54.8 28.6C99.7 390.7 159.5 400 224 400s124.3-9.3 169.2-25.3c20.8-7.4 39.9-16.9 54.8-28.6l0 85.9c0 44.2-100.3 80-224 80S0 476.2 0 432l0-85.9z"]},q={prefix:"fas",iconName:"brain",icon:[512,512,[129504],"f5dc","M184 0c30.9 0 56 25.1 56 56l0 400c0 30.9-25.1 56-56 56c-28.9 0-52.7-21.9-55.7-50.1c-5.2 1.4-10.7 2.1-16.3 2.1c-35.3 0-64-28.7-64-64c0-7.4 1.3-14.6 3.6-21.2C21.4 367.4 0 338.2 0 304c0-31.9 18.7-59.5 45.8-72.3C37.1 220.8 32 207 32 192c0-30.7 21.6-56.3 50.4-62.6C80.8 123.9 80 118 80 112c0-29.9 20.6-55.1 48.3-62.1C131.3 21.9 155.1 0 184 0zM328 0c28.9 0 52.6 21.9 55.7 49.9c27.8 7 48.3 32.1 48.3 62.1c0 6-.8 11.9-2.4 17.4c28.8 6.2 50.4 31.9 50.4 62.6c0 15-5.1 28.8-13.8 39.7C493.3 244.5 512 272.1 512 304c0 34.2-21.4 63.4-51.6 74.8c2.3 6.6 3.6 13.8 3.6 21.2c0 35.3-28.7 64-64 64c-5.6 0-11.1-.7-16.3-2.1c-3 28.2-26.8 50.1-55.7 50.1c-30.9 0-56-25.1-56-56l0-400c0-30.9 25.1-56 56-56z"]};function T(){return(0,s.jsxs)("div",{className:"flex flex-col min-h-screen bg-[#fafbfc]",children:[(0,s.jsx)(n.default,{}),(0,s.jsxs)("main",{className:"flex-grow container mx-auto px-4 py-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold mb-6",children:"Explore by Domain"}),(0,s.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:[{title:"Artificial Intelligence",description:"Explore machine learning, deep learning, and neural networks.",icon:q},{title:"Data Science",description:"Analyze and interpret complex data for insights and predictions.",icon:L},{title:"Web Development",description:"Build modern and responsive web applications.",icon:k},{title:"Mobile Development",description:"Create engaging applications for iOS and Android platforms.",icon:S},{title:"Cloud Computing",description:"Learn about cloud platforms like AWS, Azure, and Google Cloud.",icon:C},{title:"Cybersecurity",description:"Protect systems from digital threats and attacks.",icon:z},{title:"Robotics",description:"Design, build, and operate robots and automated systems.",icon:I},{title:"Database Management",description:"Master the design, implementation, and maintenance of databases.",icon:M},{title:"DevOps",description:"Combine software development and IT operations to shorten development life cycles.",icon:A},{title:"Project Management",description:"Lead and manage projects to successful completion.",icon:_},{title:"Innovation & Design",description:"Foster creativity and design thinking to solve complex problems.",icon:P}].map(e=>(0,s.jsx)(O,{title:e.title,description:e.description,icon:e.icon},e.title))})]})]})}},9021:e=>{"use strict";e.exports=require("fs")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9190:(e,t,r)=>{"use strict";r.d(t,{default:()=>l});var s=r(687),n=r(3210),a=r(5885),o=r(7e3),i=r(7346),c=r(7351);let l=()=>{let[e,t]=(0,n.useState)(!1),[r,l]=(0,n.useState)(null),d=(0,n.useRef)(null),p=e=>{try{let t=e.split(".")[1].replace(/-/g,"+").replace(/_/g,"/"),r=decodeURIComponent(atob(t).split("").map(function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)}).join(""));return JSON.parse(r)}catch(e){return console.error("Error decoding JWT:",e),null}};(0,n.useEffect)(()=>{let e=localStorage.getItem("token");if(e){let t=p(e);t&&l({id:t.id,email:t.email,username:t.username,role:t.role,first_name:t.first_name||t.username,last_name:t.last_name||""})}},[]);let u=(e,t)=>(e?e.charAt(0).toUpperCase():"")+(t?t.charAt(0).toUpperCase():"")||"U";return(0,n.useEffect)(()=>{let e=e=>{d.current&&!d.current.contains(e.target)&&t(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]),(0,s.jsxs)("nav",{className:"bg-white p-4 shadow-md",children:[(0,s.jsxs)("div",{className:"container mx-auto flex justify-between items-center",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"bg-orange-500 rounded-xl p-2 flex items-center justify-center rounded-lg",children:(0,s.jsx)(c.A,{className:"w-6 h-6 text-white"})}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("span",{className:"text-orange-500 text-lg font-bold",children:"EX"}),(0,s.jsx)("span",{className:"text-[#005071] text-lg font-bold",children:"Learn"})]})]}),(0,s.jsx)("div",{className:"flex-grow flex justify-center mx-4",children:(0,s.jsx)("div",{className:"relative w-full max-w-[280px]",children:(0,s.jsx)("input",{type:"text",placeholder:"Search courses, topics, or instructors...",className:"w-full pl-3 pr-3 py-1 rounded-full border border-transparent focus:outline-none focus:ring-1 focus:ring-orange-100 text-xs placeholder-gray-400"})})}),(0,s.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,s.jsx)(i.A,{}),(0,s.jsx)("div",{className:"relative",ref:d,children:r?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 cursor-pointer hover:bg-gray-50 rounded-lg px-2 py-1 transition-colors duration-200",onClick:()=>{t(!e)},children:[(0,s.jsx)("div",{className:"bg-orange-500 text-white rounded-full h-8 w-8 flex items-center justify-center font-medium",children:u(r.first_name,r.last_name)}),(0,s.jsx)("span",{className:"text-gray-700 text-sm font-medium",children:r.first_name}),(0,s.jsx)(a.g,{icon:o.Jt$,className:`text-gray-500 text-xs transition-transform duration-200 ${e?"rotate-180":""}`})]}),e&&(0,s.jsxs)("div",{className:"absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50",children:[(0,s.jsx)("div",{className:"px-4 py-3 border-b border-gray-100",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"bg-orange-500 text-white rounded-full h-10 w-10 flex items-center justify-center font-medium",children:u(r.first_name,r.last_name)}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{className:"text-sm font-medium text-gray-900",children:[r.first_name," ",r.last_name]}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:r.email})]})]})}),(0,s.jsxs)("div",{className:"py-1",children:[(0,s.jsxs)("button",{className:"w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200",onClick:()=>{t(!1)},children:[(0,s.jsx)(a.g,{icon:o.X46,className:"mr-3 text-gray-400"}),"Profile"]}),(0,s.jsxs)("button",{className:"w-full flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors duration-200",onClick:()=>{localStorage.removeItem("token"),sessionStorage.removeItem("token"),localStorage.removeItem("authToken"),sessionStorage.removeItem("authToken"),l(null),window.location.href="/signin"},children:[(0,s.jsx)(a.g,{icon:o.GxD,className:"mr-3 text-red-500"}),"Log out"]})]})]})]}):(0,s.jsxs)("a",{href:"/signin",className:"flex items-center space-x-2 bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors duration-200",children:[(0,s.jsx)(a.g,{icon:o.X46,className:"text-sm"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:"Sign In"})]})})]})]}),(0,s.jsx)("div",{className:"container mx-auto mt-4",children:(0,s.jsxs)("ul",{className:"flex space-x-8",children:[(0,s.jsx)("li",{children:(0,s.jsxs)("a",{href:"/",className:"flex items-center space-x-2 text-orange-500 border-b-2 border-orange-500 pb-2",children:[(0,s.jsx)(a.g,{icon:o.v02,className:"text-sm"}),(0,s.jsx)("span",{children:"Home"})]})}),(0,s.jsx)("li",{children:(0,s.jsxs)("a",{href:"/my-dashboard",className:"flex items-center space-x-2 text-gray-700 hover:text-orange-500",children:[(0,s.jsx)(a.g,{icon:o.xiI,className:"text-sm"}),(0,s.jsx)("span",{children:"My Dashboard"})]})}),(0,s.jsx)("li",{children:(0,s.jsxs)("a",{href:"/my-learning",className:"flex items-center space-x-2 text-gray-700 hover:text-orange-500",children:[(0,s.jsx)(a.g,{icon:o.ReK,className:"text-sm"}),(0,s.jsx)("span",{children:"My Learning"})]})}),(0,s.jsx)("li",{children:(0,s.jsxs)("a",{href:"/my-certificates",className:"flex items-center space-x-2 text-gray-700 hover:text-orange-500",children:[(0,s.jsx)(a.g,{icon:o.fmL,className:"text-sm"}),(0,s.jsx)("span",{children:"Certificates"})]})})]})})]})}},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9551:e=>{"use strict";e.exports=require("url")},9646:e=>{"use strict";e.exports=require("child_process")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,773,658,578],()=>r(6395));module.exports=s})();