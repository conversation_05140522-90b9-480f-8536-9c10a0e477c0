"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[578],{968:(e,t,n)=>{n.d(t,{b:()=>a});var r=n(2115),o=n(3655),i=n(5155),l=r.forwardRef((e,t)=>(0,i.jsx)(o.sG.label,{...e,ref:t,onMouseDown:t=>{var n;t.target.closest("button, input, select, textarea")||(null==(n=e.onMouseDown)||n.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var a=l},1285:(e,t,n)=>{n.d(t,{B:()=>u});var r,o=n(2115),i=n(2712),l=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),a=0;function u(e){let[t,n]=o.useState(l());return(0,i.N)(()=>{e||n(e=>e??String(a++))},[e]),e||(t?`radix-${t}`:"")}},2293:(e,t,n)=>{n.d(t,{Oh:()=>i});var r=n(2115),o=0;function i(){r.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:l()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:l()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function l(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},2564:(e,t,n)=>{n.d(t,{Qg:()=>l,bL:()=>u});var r=n(2115),o=n(3655),i=n(5155),l=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),a=r.forwardRef((e,t)=>(0,i.jsx)(o.sG.span,{...e,ref:t,style:{...l,...e.style}}));a.displayName="VisuallyHidden";var u=a},2712:(e,t,n)=>{n.d(t,{N:()=>o});var r=n(2115),o=globalThis?.document?r.useLayoutEffect:()=>{}},3655:(e,t,n)=>{n.d(t,{hO:()=>u,sG:()=>a});var r=n(2115),o=n(7650),i=n(9708),l=n(5155),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,i.TL)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(o?n:t,{...i,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function u(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},3795:(e,t,n)=>{n.d(t,{A:()=>$});var r,o,i=function(){return(i=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function l(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var a=("function"==typeof SuppressedError&&SuppressedError,n(2115)),u="right-scroll-bar-position",c="width-before-scroll-bar";function s(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var d="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,f=new WeakMap;function p(e){return e}var h=function(e){void 0===e&&(e={});var t,n,r,o,l=(t=null,void 0===n&&(n=p),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var i=function(){var n=t;t=[],n.forEach(e)},l=function(){return Promise.resolve().then(i)};l(),r={push:function(e){t.push(e),l()},filter:function(e){return t=t.filter(e),r}}}});return l.options=i({async:!0,ssr:!1},e),l}(),v=function(){},m=a.forwardRef(function(e,t){var n,r,o,u,c=a.useRef(null),p=a.useState({onScrollCapture:v,onWheelCapture:v,onTouchMoveCapture:v}),m=p[0],g=p[1],y=e.forwardProps,w=e.children,b=e.className,x=e.removeScrollBar,E=e.enabled,S=e.shards,C=e.sideCar,R=e.noRelative,A=e.noIsolation,T=e.inert,L=e.allowPinchZoom,k=e.as,P=e.gapMode,N=l(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),M=(n=[c,t],r=function(e){return n.forEach(function(t){return s(t,e)})},(o=(0,a.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,u=o.facade,d(function(){var e=f.get(u);if(e){var t=new Set(e),r=new Set(n),o=u.current;t.forEach(function(e){r.has(e)||s(e,null)}),r.forEach(function(e){t.has(e)||s(e,o)})}f.set(u,n)},[n]),u),j=i(i({},N),m);return a.createElement(a.Fragment,null,E&&a.createElement(C,{sideCar:h,removeScrollBar:x,shards:S,noRelative:R,noIsolation:A,inert:T,setCallbacks:g,allowPinchZoom:!!L,lockRef:c,gapMode:P}),y?a.cloneElement(a.Children.only(w),i(i({},j),{ref:M})):a.createElement(void 0===k?"div":k,i({},j,{className:b,ref:M}),w))});m.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},m.classNames={fullWidth:c,zeroRight:u};var g=function(e){var t=e.sideCar,n=l(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return a.createElement(r,i({},n))};g.isSideCarExport=!0;var y=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,l;(i=t).styleSheet?i.styleSheet.cssText=r:i.appendChild(document.createTextNode(r)),l=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(l)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},w=function(){var e=y();return function(t,n){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},b=function(){var e=w();return function(t){return e(t.styles,t.dynamic),null}},x={left:0,top:0,right:0,gap:0},E=function(e){return parseInt(e||"",10)||0},S=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[E(n),E(r),E(o)]},C=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return x;var t=S(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},R=b(),A="data-scroll-locked",T=function(e,t,n,r){var o=e.left,i=e.top,l=e.right,a=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(a,"px ").concat(r,";\n  }\n  body[").concat(A,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(l,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(a,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(u," {\n    right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(c," {\n    margin-right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(c," .").concat(c," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(A,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(a,"px;\n  }\n")},L=function(){var e=parseInt(document.body.getAttribute(A)||"0",10);return isFinite(e)?e:0},k=function(){a.useEffect(function(){return document.body.setAttribute(A,(L()+1).toString()),function(){var e=L()-1;e<=0?document.body.removeAttribute(A):document.body.setAttribute(A,e.toString())}},[])},P=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;k();var i=a.useMemo(function(){return C(o)},[o]);return a.createElement(R,{styles:T(i,!t,o,n?"":"!important")})},N=!1;if("undefined"!=typeof window)try{var M=Object.defineProperty({},"passive",{get:function(){return N=!0,!0}});window.addEventListener("test",M,M),window.removeEventListener("test",M,M)}catch(e){N=!1}var j=!!N&&{passive:!1},O=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},D=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),I(e,r)){var o=W(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},I=function(e,t){return"v"===e?O(t,"overflowY"):O(t,"overflowX")},W=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},B=function(e,t,n,r,o){var i,l=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),a=l*r,u=n.target,c=t.contains(u),s=!1,d=a>0,f=0,p=0;do{if(!u)break;var h=W(e,u),v=h[0],m=h[1]-h[2]-l*v;(v||m)&&I(e,u)&&(f+=m,p+=v);var g=u.parentNode;u=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&a>f)?s=!0:!d&&(o&&1>Math.abs(p)||!o&&-a>p)&&(s=!0),s},F=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},H=function(e){return[e.deltaX,e.deltaY]},_=function(e){return e&&"current"in e?e.current:e},z=0,V=[];let G=(r=function(e){var t=a.useRef([]),n=a.useRef([0,0]),r=a.useRef(),o=a.useState(z++)[0],i=a.useState(b)[0],l=a.useRef(e);a.useEffect(function(){l.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(_),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=a.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!l.current.allowPinchZoom;var o,i=F(e),a=n.current,u="deltaX"in e?e.deltaX:a[0]-i[0],c="deltaY"in e?e.deltaY:a[1]-i[1],s=e.target,d=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=D(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=D(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||c)&&(r.current=o),!o)return!0;var p=r.current||o;return B(p,t,e,"h"===p?u:c,!0)},[]),c=a.useCallback(function(e){if(V.length&&V[V.length-1]===i){var n="deltaY"in e?H(e):F(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(l.current.shards||[]).map(_).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!l.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=a.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=a.useCallback(function(e){n.current=F(e),r.current=void 0},[]),f=a.useCallback(function(t){s(t.type,H(t),t.target,u(t,e.lockRef.current))},[]),p=a.useCallback(function(t){s(t.type,F(t),t.target,u(t,e.lockRef.current))},[]);a.useEffect(function(){return V.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",c,j),document.addEventListener("touchmove",c,j),document.addEventListener("touchstart",d,j),function(){V=V.filter(function(e){return e!==i}),document.removeEventListener("wheel",c,j),document.removeEventListener("touchmove",c,j),document.removeEventListener("touchstart",d,j)}},[]);var h=e.removeScrollBar,v=e.inert;return a.createElement(a.Fragment,null,v?a.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?a.createElement(P,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},h.useMedium(r),g);var K=a.forwardRef(function(e,t){return a.createElement(m,i({},e,{ref:t,sideCar:G}))});K.classNames=m.classNames;let $=K},4378:(e,t,n)=>{n.d(t,{Z:()=>u});var r=n(2115),o=n(7650),i=n(3655),l=n(2712),a=n(5155),u=r.forwardRef((e,t)=>{var n,u;let{container:c,...s}=e,[d,f]=r.useState(!1);(0,l.N)(()=>f(!0),[]);let p=c||d&&(null==(u=globalThis)||null==(n=u.document)?void 0:n.body);return p?o.createPortal((0,a.jsx)(i.sG.div,{...s,ref:t}),p):null});u.displayName="Portal"},5185:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},5196:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5845:(e,t,n)=>{n.d(t,{i:()=>a});var r,o=n(2115),i=n(2712),l=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||i.N;function a({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[i,a,u]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),i=o.useRef(n),a=o.useRef(t);return l(()=>{a.current=t},[t]),o.useEffect(()=>{i.current!==n&&(a.current?.(n),i.current=n)},[n,i]),[n,r,a]}({defaultProp:t,onChange:n}),c=void 0!==e,s=c?e:i;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,r])}return[s,o.useCallback(t=>{if(c){let n="function"==typeof t?t(e):t;n!==e&&u.current?.(n)}else a(t)},[c,e,a,u])]}Symbol("RADIX:SYNC_STATE")},6081:(e,t,n)=>{n.d(t,{A:()=>l,q:()=>i});var r=n(2115),o=n(5155);function i(e,t){let n=r.createContext(t),i=e=>{let{children:t,...i}=e,l=r.useMemo(()=>i,Object.values(i));return(0,o.jsx)(n.Provider,{value:l,children:t})};return i.displayName=e+"Provider",[i,function(o){let i=r.useContext(n);if(i)return i;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function l(e,t=[]){let n=[],i=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return i.scopeName=e,[function(t,i){let l=r.createContext(i),a=n.length;n=[...n,i];let u=t=>{let{scope:n,children:i,...u}=t,c=n?.[e]?.[a]||l,s=r.useMemo(()=>u,Object.values(u));return(0,o.jsx)(c.Provider,{value:s,children:i})};return u.displayName=t+"Provider",[u,function(n,o){let u=o?.[e]?.[a]||l,c=r.useContext(u);if(c)return c;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(i,...t)]}},6474:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},7863:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},7900:(e,t,n)=>{n.d(t,{n:()=>d});var r=n(2115),o=n(6101),i=n(3655),l=n(9033),a=n(5155),u="focusScope.autoFocusOnMount",c="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},d=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:d=!1,onMountAutoFocus:m,onUnmountAutoFocus:g,...y}=e,[w,b]=r.useState(null),x=(0,l.c)(m),E=(0,l.c)(g),S=r.useRef(null),C=(0,o.s)(t,e=>b(e)),R=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(d){let e=function(e){if(R.paused||!w)return;let t=e.target;w.contains(t)?S.current=t:h(S.current,{select:!0})},t=function(e){if(R.paused||!w)return;let t=e.relatedTarget;null!==t&&(w.contains(t)||h(S.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&h(w)});return w&&n.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[d,w,R.paused]),r.useEffect(()=>{if(w){v.add(R);let e=document.activeElement;if(!w.contains(e)){let t=new CustomEvent(u,s);w.addEventListener(u,x),w.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(h(r,{select:t}),document.activeElement!==n)return}(f(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&h(w))}return()=>{w.removeEventListener(u,x),setTimeout(()=>{let t=new CustomEvent(c,s);w.addEventListener(c,E),w.dispatchEvent(t),t.defaultPrevented||h(null!=e?e:document.body,{select:!0}),w.removeEventListener(c,E),v.remove(R)},0)}}},[w,x,E,R]);let A=r.useCallback(e=>{if(!n&&!d||R.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,i]=function(e){let t=f(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&i?e.shiftKey||r!==i?e.shiftKey&&r===o&&(e.preventDefault(),n&&h(i,{select:!0})):(e.preventDefault(),n&&h(o,{select:!0})):r===t&&e.preventDefault()}},[n,d,R.paused]);return(0,a.jsx)(i.sG.div,{tabIndex:-1,...y,ref:C,onKeyDown:A})});function f(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function h(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}d.displayName="FocusScope";var v=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=m(e,t)).unshift(t)},remove(t){var n;null==(n=(e=m(e,t))[0])||n.resume()}}}();function m(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},8168:(e,t,n)=>{n.d(t,{Eq:()=>s});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,i=new WeakMap,l={},a=0,u=function(e){return e&&(e.host||u(e.parentNode))},c=function(e,t,n,r){var c=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=u(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});l[n]||(l[n]=new WeakMap);var s=l[n],d=[],f=new Set,p=new Set(c),h=function(e){!e||f.has(e)||(f.add(e),h(e.parentNode))};c.forEach(h);var v=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))v(e);else try{var t=e.getAttribute(r),l=null!==t&&"false"!==t,a=(o.get(e)||0)+1,u=(s.get(e)||0)+1;o.set(e,a),s.set(e,u),d.push(e),1===a&&l&&i.set(e,!0),1===u&&e.setAttribute(n,"true"),l||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return v(t),f.clear(),a++,function(){d.forEach(function(e){var t=o.get(e)-1,l=s.get(e)-1;o.set(e,t),s.set(e,l),t||(i.has(e)||e.removeAttribute(r),i.delete(e)),l||e.removeAttribute(n)}),--a||(o=new WeakMap,o=new WeakMap,i=new WeakMap,l={})}},s=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),i=t||r(e);return i?(o.push.apply(o,Array.from(i.querySelectorAll("[aria-live], script"))),c(o,i,n,"aria-hidden")):function(){return null}}},8795:(e,t,n)=>{n.d(t,{Mz:()=>eY,i3:()=>eZ,UC:()=>eX,bL:()=>eq,Bk:()=>eM});var r=n(2115);let o=["top","right","bottom","left"],i=Math.min,l=Math.max,a=Math.round,u=Math.floor,c=e=>({x:e,y:e}),s={left:"right",right:"left",bottom:"top",top:"bottom"},d={start:"end",end:"start"};function f(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function v(e){return"x"===e?"y":"x"}function m(e){return"y"===e?"height":"width"}function g(e){return["top","bottom"].includes(p(e))?"y":"x"}function y(e){return e.replace(/start|end/g,e=>d[e])}function w(e){return e.replace(/left|right|bottom|top/g,e=>s[e])}function b(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function x(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function E(e,t,n){let r,{reference:o,floating:i}=e,l=g(t),a=v(g(t)),u=m(a),c=p(t),s="y"===l,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,y=o[u]/2-i[u]/2;switch(c){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-i.width,y:f};break;default:r={x:o.x,y:o.y}}switch(h(t)){case"start":r[a]-=y*(n&&s?-1:1);break;case"end":r[a]+=y*(n&&s?-1:1)}return r}let S=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,a=i.filter(Boolean),u=await (null==l.isRTL?void 0:l.isRTL(t)),c=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:d}=E(c,r,u),f=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:i,fn:v}=a[n],{x:m,y:g,data:y,reset:w}=await v({x:s,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:c,platform:l,elements:{reference:e,floating:t}});s=null!=m?m:s,d=null!=g?g:d,p={...p,[i]:{...p[i],...y}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(c=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:d}=E(c,f,u)),n=-1)}return{x:s,y:d,placement:f,strategy:o,middlewareData:p}};async function C(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:l,elements:a,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:d="floating",altBoundary:p=!1,padding:h=0}=f(t,e),v=b(h),m=a[p?"floating"===d?"reference":"floating":d],g=x(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(m)))||n?m:m.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:c,rootBoundary:s,strategy:u})),y="floating"===d?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,w=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),E=await (null==i.isElement?void 0:i.isElement(w))&&await (null==i.getScale?void 0:i.getScale(w))||{x:1,y:1},S=x(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:y,offsetParent:w,strategy:u}):y);return{top:(g.top-S.top+v.top)/E.y,bottom:(S.bottom-g.bottom+v.bottom)/E.y,left:(g.left-S.left+v.left)/E.x,right:(S.right-g.right+v.right)/E.x}}function R(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function A(e){return o.some(t=>e[t]>=0)}async function T(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),l=p(n),a=h(n),u="y"===g(n),c=["left","top"].includes(l)?-1:1,s=i&&u?-1:1,d=f(t,e),{mainAxis:v,crossAxis:m,alignmentAxis:y}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&"number"==typeof y&&(m="end"===a?-1*y:y),u?{x:m*s,y:v*c}:{x:v*c,y:m*s}}function L(){return"undefined"!=typeof window}function k(e){return M(e)?(e.nodeName||"").toLowerCase():"#document"}function P(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function N(e){var t;return null==(t=(M(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function M(e){return!!L()&&(e instanceof Node||e instanceof P(e).Node)}function j(e){return!!L()&&(e instanceof Element||e instanceof P(e).Element)}function O(e){return!!L()&&(e instanceof HTMLElement||e instanceof P(e).HTMLElement)}function D(e){return!!L()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof P(e).ShadowRoot)}function I(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=_(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function W(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function B(e){let t=F(),n=j(e)?_(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function F(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function H(e){return["html","body","#document"].includes(k(e))}function _(e){return P(e).getComputedStyle(e)}function z(e){return j(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function V(e){if("html"===k(e))return e;let t=e.assignedSlot||e.parentNode||D(e)&&e.host||N(e);return D(t)?t.host:t}function G(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=V(t);return H(n)?t.ownerDocument?t.ownerDocument.body:t.body:O(n)&&I(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=P(o);if(i){let e=K(l);return t.concat(l,l.visualViewport||[],I(o)?o:[],e&&n?G(e):[])}return t.concat(o,G(o,[],n))}function K(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function $(e){let t=_(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=O(e),i=o?e.offsetWidth:n,l=o?e.offsetHeight:r,u=a(n)!==i||a(r)!==l;return u&&(n=i,r=l),{width:n,height:r,$:u}}function U(e){return j(e)?e:e.contextElement}function q(e){let t=U(e);if(!O(t))return c(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=$(t),l=(i?a(n.width):n.width)/r,u=(i?a(n.height):n.height)/o;return l&&Number.isFinite(l)||(l=1),u&&Number.isFinite(u)||(u=1),{x:l,y:u}}let Y=c(0);function X(e){let t=P(e);return F()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:Y}function Z(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),l=U(e),a=c(1);t&&(r?j(r)&&(a=q(r)):a=q(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===P(l))&&o)?X(l):c(0),s=(i.left+u.x)/a.x,d=(i.top+u.y)/a.y,f=i.width/a.x,p=i.height/a.y;if(l){let e=P(l),t=r&&j(r)?P(r):r,n=e,o=K(n);for(;o&&r&&t!==n;){let e=q(o),t=o.getBoundingClientRect(),r=_(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;s*=e.x,d*=e.y,f*=e.x,p*=e.y,s+=i,d+=l,o=K(n=P(o))}}return x({width:f,height:p,x:s,y:d})}function Q(e,t){let n=z(e).scrollLeft;return t?t.left+n:Z(N(e)).left+n}function J(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:Q(e,r)),y:r.top+t.scrollTop}}function ee(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=P(e),r=N(e),o=n.visualViewport,i=r.clientWidth,l=r.clientHeight,a=0,u=0;if(o){i=o.width,l=o.height;let e=F();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,u=o.offsetTop)}return{width:i,height:l,x:a,y:u}}(e,n);else if("document"===t)r=function(e){let t=N(e),n=z(e),r=e.ownerDocument.body,o=l(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=l(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+Q(e),u=-n.scrollTop;return"rtl"===_(r).direction&&(a+=l(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:a,y:u}}(N(e));else if(j(t))r=function(e,t){let n=Z(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=O(e)?q(e):c(1),l=e.clientWidth*i.x,a=e.clientHeight*i.y;return{width:l,height:a,x:o*i.x,y:r*i.y}}(t,n);else{let n=X(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return x(r)}function et(e){return"static"===_(e).position}function en(e,t){if(!O(e)||"fixed"===_(e).position)return null;if(t)return t(e);let n=e.offsetParent;return N(e)===n&&(n=n.ownerDocument.body),n}function er(e,t){let n=P(e);if(W(e))return n;if(!O(e)){let t=V(e);for(;t&&!H(t);){if(j(t)&&!et(t))return t;t=V(t)}return n}let r=en(e,t);for(;r&&["table","td","th"].includes(k(r))&&et(r);)r=en(r,t);return r&&H(r)&&et(r)&&!B(r)?n:r||function(e){let t=V(e);for(;O(t)&&!H(t);){if(B(t))return t;if(W(t))break;t=V(t)}return null}(e)||n}let eo=async function(e){let t=this.getOffsetParent||er,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=O(t),o=N(t),i="fixed"===n,l=Z(e,!0,i,t),a={scrollLeft:0,scrollTop:0},u=c(0);if(r||!r&&!i)if(("body"!==k(t)||I(o))&&(a=z(t)),r){let e=Z(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=Q(o));i&&!r&&o&&(u.x=Q(o));let s=!o||r||i?c(0):J(o,a);return{x:l.left+a.scrollLeft-u.x-s.x,y:l.top+a.scrollTop-u.y-s.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ei={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,l=N(r),a=!!t&&W(t.floating);if(r===l||a&&i)return n;let u={scrollLeft:0,scrollTop:0},s=c(1),d=c(0),f=O(r);if((f||!f&&!i)&&(("body"!==k(r)||I(l))&&(u=z(r)),O(r))){let e=Z(r);s=q(r),d.x=e.x+r.clientLeft,d.y=e.y+r.clientTop}let p=!l||f||i?c(0):J(l,u,!0);return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-u.scrollLeft*s.x+d.x+p.x,y:n.y*s.y-u.scrollTop*s.y+d.y+p.y}},getDocumentElement:N,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,a=[..."clippingAncestors"===n?W(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=G(e,[],!1).filter(e=>j(e)&&"body"!==k(e)),o=null,i="fixed"===_(e).position,l=i?V(e):e;for(;j(l)&&!H(l);){let t=_(l),n=B(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||I(l)&&!n&&function e(t,n){let r=V(t);return!(r===n||!j(r)||H(r))&&("fixed"===_(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):o=t,l=V(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],u=a[0],c=a.reduce((e,n)=>{let r=ee(t,n,o);return e.top=l(r.top,e.top),e.right=i(r.right,e.right),e.bottom=i(r.bottom,e.bottom),e.left=l(r.left,e.left),e},ee(t,u,o));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}},getOffsetParent:er,getElementRects:eo,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=$(e);return{width:t,height:n}},getScale:q,isElement:j,isRTL:function(e){return"rtl"===_(e).direction}};function el(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ea=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:a,platform:u,elements:c,middlewareData:s}=t,{element:d,padding:p=0}=f(e,t)||{};if(null==d)return{};let y=b(p),w={x:n,y:r},x=v(g(o)),E=m(x),S=await u.getDimensions(d),C="y"===x,R=C?"clientHeight":"clientWidth",A=a.reference[E]+a.reference[x]-w[x]-a.floating[E],T=w[x]-a.reference[x],L=await (null==u.getOffsetParent?void 0:u.getOffsetParent(d)),k=L?L[R]:0;k&&await (null==u.isElement?void 0:u.isElement(L))||(k=c.floating[R]||a.floating[E]);let P=k/2-S[E]/2-1,N=i(y[C?"top":"left"],P),M=i(y[C?"bottom":"right"],P),j=k-S[E]-M,O=k/2-S[E]/2+(A/2-T/2),D=l(N,i(O,j)),I=!s.arrow&&null!=h(o)&&O!==D&&a.reference[E]/2-(O<N?N:M)-S[E]/2<0,W=I?O<N?O-N:O-j:0;return{[x]:w[x]+W,data:{[x]:D,centerOffset:O-D-W,...I&&{alignmentOffset:W}},reset:I}}}),eu=(e,t,n)=>{let r=new Map,o={platform:ei,...n},i={...o.platform,_c:r};return S(e,t,{...o,platform:i})};var ec=n(7650),es="undefined"!=typeof document?r.useLayoutEffect:function(){};function ed(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!ed(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!ed(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ef(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ep(e,t){let n=ef(e);return Math.round(t*n)/n}function eh(e){let t=r.useRef(e);return es(()=>{t.current=e}),t}let ev=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ea({element:n.current,padding:r}).fn(t):{}:n?ea({element:n,padding:r}).fn(t):{}}}),em=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:l,middlewareData:a}=t,u=await T(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:l}}}}}(e),options:[e,t]}),eg=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:a=!0,crossAxis:u=!1,limiter:c={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=f(e,t),d={x:n,y:r},h=await C(t,s),m=g(p(o)),y=v(m),w=d[y],b=d[m];if(a){let e="y"===y?"top":"left",t="y"===y?"bottom":"right",n=w+h[e],r=w-h[t];w=l(n,i(w,r))}if(u){let e="y"===m?"top":"left",t="y"===m?"bottom":"right",n=b+h[e],r=b-h[t];b=l(n,i(b,r))}let x=c.fn({...t,[y]:w,[m]:b});return{...x,data:{x:x.x-n,y:x.y-r,enabled:{[y]:a,[m]:u}}}}}}(e),options:[e,t]}),ey=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:l}=t,{offset:a=0,mainAxis:u=!0,crossAxis:c=!0}=f(e,t),s={x:n,y:r},d=g(o),h=v(d),m=s[h],y=s[d],w=f(a,t),b="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(u){let e="y"===h?"height":"width",t=i.reference[h]-i.floating[e]+b.mainAxis,n=i.reference[h]+i.reference[e]-b.mainAxis;m<t?m=t:m>n&&(m=n)}if(c){var x,E;let e="y"===h?"width":"height",t=["top","left"].includes(p(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(x=l.offset)?void 0:x[d])||0)+(t?0:b.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(E=l.offset)?void 0:E[d])||0)-(t?b.crossAxis:0);y<n?y=n:y>r&&(y=r)}return{[h]:m,[d]:y}}}}(e),options:[e,t]}),ew=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,l;let{placement:a,middlewareData:u,rects:c,initialPlacement:s,platform:d,elements:b}=t,{mainAxis:x=!0,crossAxis:E=!0,fallbackPlacements:S,fallbackStrategy:R="bestFit",fallbackAxisSideDirection:A="none",flipAlignment:T=!0,...L}=f(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let k=p(a),P=g(s),N=p(s)===s,M=await (null==d.isRTL?void 0:d.isRTL(b.floating)),j=S||(N||!T?[w(s)]:function(e){let t=w(e);return[y(e),t,y(t)]}(s)),O="none"!==A;!S&&O&&j.push(...function(e,t,n,r){let o=h(e),i=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(p(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(y)))),i}(s,T,A,M));let D=[s,...j],I=await C(t,L),W=[],B=(null==(r=u.flip)?void 0:r.overflows)||[];if(x&&W.push(I[k]),E){let e=function(e,t,n){void 0===n&&(n=!1);let r=h(e),o=v(g(e)),i=m(o),l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=w(l)),[l,w(l)]}(a,c,M);W.push(I[e[0]],I[e[1]])}if(B=[...B,{placement:a,overflows:W}],!W.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=D[e];if(t&&("alignment"!==E||P===g(t)||B.every(e=>e.overflows[0]>0&&g(e.placement)===P)))return{data:{index:e,overflows:B},reset:{placement:t}};let n=null==(i=B.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(R){case"bestFit":{let e=null==(l=B.filter(e=>{if(O){let t=g(e.placement);return t===P||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=s}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),eb=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,a,{placement:u,rects:c,platform:s,elements:d}=t,{apply:v=()=>{},...m}=f(e,t),y=await C(t,m),w=p(u),b=h(u),x="y"===g(u),{width:E,height:S}=c.floating;"top"===w||"bottom"===w?(o=w,a=b===(await (null==s.isRTL?void 0:s.isRTL(d.floating))?"start":"end")?"left":"right"):(a=w,o="end"===b?"top":"bottom");let R=S-y.top-y.bottom,A=E-y.left-y.right,T=i(S-y[o],R),L=i(E-y[a],A),k=!t.middlewareData.shift,P=T,N=L;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(N=A),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(P=R),k&&!b){let e=l(y.left,0),t=l(y.right,0),n=l(y.top,0),r=l(y.bottom,0);x?N=E-2*(0!==e||0!==t?e+t:l(y.left,y.right)):P=S-2*(0!==n||0!==r?n+r:l(y.top,y.bottom))}await v({...t,availableWidth:N,availableHeight:P});let M=await s.getDimensions(d.floating);return E!==M.width||S!==M.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),ex=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=f(e,t);switch(r){case"referenceHidden":{let e=R(await C(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:A(e)}}}case"escaped":{let e=R(await C(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:A(e)}}}default:return{}}}}}(e),options:[e,t]}),eE=(e,t)=>({...ev(e),options:[e,t]});var eS=n(3655),eC=n(5155),eR=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,eC.jsx)(eS.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eC.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eR.displayName="Arrow";var eA=n(6101),eT=n(6081),eL=n(9033),ek=n(2712),eP="Popper",[eN,eM]=(0,eT.A)(eP),[ej,eO]=eN(eP),eD=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,eC.jsx)(ej,{scope:t,anchor:o,onAnchorChange:i,children:n})};eD.displayName=eP;var eI="PopperAnchor",eW=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...i}=e,l=eO(eI,n),a=r.useRef(null),u=(0,eA.s)(t,a);return r.useEffect(()=>{l.onAnchorChange((null==o?void 0:o.current)||a.current)}),o?null:(0,eC.jsx)(eS.sG.div,{...i,ref:u})});eW.displayName=eI;var eB="PopperContent",[eF,eH]=eN(eB),e_=r.forwardRef((e,t)=>{var n,o,a,c,s,d,f,p;let{__scopePopper:h,side:v="bottom",sideOffset:m=0,align:g="center",alignOffset:y=0,arrowPadding:w=0,avoidCollisions:b=!0,collisionBoundary:x=[],collisionPadding:E=0,sticky:S="partial",hideWhenDetached:C=!1,updatePositionStrategy:R="optimized",onPlaced:A,...T}=e,L=eO(eB,h),[k,P]=r.useState(null),M=(0,eA.s)(t,e=>P(e)),[j,O]=r.useState(null),D=function(e){let[t,n]=r.useState(void 0);return(0,ek.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(j),I=null!=(f=null==D?void 0:D.width)?f:0,W=null!=(p=null==D?void 0:D.height)?p:0,B="number"==typeof E?E:{top:0,right:0,bottom:0,left:0,...E},F=Array.isArray(x)?x:[x],H=F.length>0,_={padding:B,boundary:F.filter(eK),altBoundary:H},{refs:z,floatingStyles:V,placement:K,isPositioned:$,middlewareData:q}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:i,elements:{reference:l,floating:a}={},transform:u=!0,whileElementsMounted:c,open:s}=e,[d,f]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(o);ed(p,o)||h(o);let[v,m]=r.useState(null),[g,y]=r.useState(null),w=r.useCallback(e=>{e!==S.current&&(S.current=e,m(e))},[]),b=r.useCallback(e=>{e!==C.current&&(C.current=e,y(e))},[]),x=l||v,E=a||g,S=r.useRef(null),C=r.useRef(null),R=r.useRef(d),A=null!=c,T=eh(c),L=eh(i),k=eh(s),P=r.useCallback(()=>{if(!S.current||!C.current)return;let e={placement:t,strategy:n,middleware:p};L.current&&(e.platform=L.current),eu(S.current,C.current,e).then(e=>{let t={...e,isPositioned:!1!==k.current};N.current&&!ed(R.current,t)&&(R.current=t,ec.flushSync(()=>{f(t)}))})},[p,t,n,L,k]);es(()=>{!1===s&&R.current.isPositioned&&(R.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[s]);let N=r.useRef(!1);es(()=>(N.current=!0,()=>{N.current=!1}),[]),es(()=>{if(x&&(S.current=x),E&&(C.current=E),x&&E){if(T.current)return T.current(x,E,P);P()}},[x,E,P,T,A]);let M=r.useMemo(()=>({reference:S,floating:C,setReference:w,setFloating:b}),[w,b]),j=r.useMemo(()=>({reference:x,floating:E}),[x,E]),O=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!j.floating)return e;let t=ep(j.floating,d.x),r=ep(j.floating,d.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...ef(j.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,j.floating,d.x,d.y]);return r.useMemo(()=>({...d,update:P,refs:M,elements:j,floatingStyles:O}),[d,P,M,j,O])}({strategy:"fixed",placement:v+("center"!==g?"-"+g:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:a=!0,ancestorResize:c=!0,elementResize:s="function"==typeof ResizeObserver,layoutShift:d="function"==typeof IntersectionObserver,animationFrame:f=!1}=r,p=U(e),h=a||c?[...p?G(p):[],...G(t)]:[];h.forEach(e=>{a&&e.addEventListener("scroll",n,{passive:!0}),c&&e.addEventListener("resize",n)});let v=p&&d?function(e,t){let n,r=null,o=N(e);function a(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function c(s,d){void 0===s&&(s=!1),void 0===d&&(d=1),a();let f=e.getBoundingClientRect(),{left:p,top:h,width:v,height:m}=f;if(s||t(),!v||!m)return;let g=u(h),y=u(o.clientWidth-(p+v)),w={rootMargin:-g+"px "+-y+"px "+-u(o.clientHeight-(h+m))+"px "+-u(p)+"px",threshold:l(0,i(1,d))||1},b=!0;function x(t){let r=t[0].intersectionRatio;if(r!==d){if(!b)return c();r?c(!1,r):n=setTimeout(()=>{c(!1,1e-7)},1e3)}1!==r||el(f,e.getBoundingClientRect())||c(),b=!1}try{r=new IntersectionObserver(x,{...w,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(x,w)}r.observe(e)}(!0),a}(p,n):null,m=-1,g=null;s&&(g=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),n()}),p&&!f&&g.observe(p),g.observe(t));let y=f?Z(e):null;return f&&function t(){let r=Z(e);y&&!el(y,r)&&n(),y=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{a&&e.removeEventListener("scroll",n),c&&e.removeEventListener("resize",n)}),null==v||v(),null==(e=g)||e.disconnect(),g=null,f&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===R})},elements:{reference:L.anchor},middleware:[em({mainAxis:m+W,alignmentAxis:y}),b&&eg({mainAxis:!0,crossAxis:!1,limiter:"partial"===S?ey():void 0,..._}),b&&ew({..._}),eb({..._,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:i,height:l}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(i,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),j&&eE({element:j,padding:w}),e$({arrowWidth:I,arrowHeight:W}),C&&ex({strategy:"referenceHidden",..._})]}),[Y,X]=eU(K),Q=(0,eL.c)(A);(0,ek.N)(()=>{$&&(null==Q||Q())},[$,Q]);let J=null==(n=q.arrow)?void 0:n.x,ee=null==(o=q.arrow)?void 0:o.y,et=(null==(a=q.arrow)?void 0:a.centerOffset)!==0,[en,er]=r.useState();return(0,ek.N)(()=>{k&&er(window.getComputedStyle(k).zIndex)},[k]),(0,eC.jsx)("div",{ref:z.setFloating,"data-radix-popper-content-wrapper":"",style:{...V,transform:$?V.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:en,"--radix-popper-transform-origin":[null==(c=q.transformOrigin)?void 0:c.x,null==(s=q.transformOrigin)?void 0:s.y].join(" "),...(null==(d=q.hide)?void 0:d.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eC.jsx)(eF,{scope:h,placedSide:Y,onArrowChange:O,arrowX:J,arrowY:ee,shouldHideArrow:et,children:(0,eC.jsx)(eS.sG.div,{"data-side":Y,"data-align":X,...T,ref:M,style:{...T.style,animation:$?void 0:"none"}})})})});e_.displayName=eB;var ez="PopperArrow",eV={top:"bottom",right:"left",bottom:"top",left:"right"},eG=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=eH(ez,n),i=eV[o.placedSide];return(0,eC.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eC.jsx)(eR,{...r,ref:t,style:{...r.style,display:"block"}})})});function eK(e){return null!==e}eG.displayName=ez;var e$=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,l;let{placement:a,rects:u,middlewareData:c}=t,s=(null==(n=c.arrow)?void 0:n.centerOffset)!==0,d=s?0:e.arrowWidth,f=s?0:e.arrowHeight,[p,h]=eU(a),v={start:"0%",center:"50%",end:"100%"}[h],m=(null!=(i=null==(r=c.arrow)?void 0:r.x)?i:0)+d/2,g=(null!=(l=null==(o=c.arrow)?void 0:o.y)?l:0)+f/2,y="",w="";return"bottom"===p?(y=s?v:"".concat(m,"px"),w="".concat(-f,"px")):"top"===p?(y=s?v:"".concat(m,"px"),w="".concat(u.floating.height+f,"px")):"right"===p?(y="".concat(-f,"px"),w=s?v:"".concat(g,"px")):"left"===p&&(y="".concat(u.floating.width+f,"px"),w=s?v:"".concat(g,"px")),{data:{x:y,y:w}}}});function eU(e){let[t,n="center"]=e.split("-");return[t,n]}var eq=eD,eY=eW,eX=e_,eZ=eG},9033:(e,t,n)=>{n.d(t,{c:()=>o});var r=n(2115);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},9178:(e,t,n)=>{n.d(t,{qW:()=>f});var r,o=n(2115),i=n(5185),l=n(3655),a=n(6101),u=n(9033),c=n(5155),s="dismissableLayer.update",d=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{var n,f;let{disableOutsidePointerEvents:v=!1,onEscapeKeyDown:m,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:w,onDismiss:b,...x}=e,E=o.useContext(d),[S,C]=o.useState(null),R=null!=(f=null==S?void 0:S.ownerDocument)?f:null==(n=globalThis)?void 0:n.document,[,A]=o.useState({}),T=(0,a.s)(t,e=>C(e)),L=Array.from(E.layers),[k]=[...E.layersWithOutsidePointerEventsDisabled].slice(-1),P=L.indexOf(k),N=S?L.indexOf(S):-1,M=E.layersWithOutsidePointerEventsDisabled.size>0,j=N>=P,O=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,u.c)(e),i=o.useRef(!1),l=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){h("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",l.current),l.current=t,n.addEventListener("click",l.current,{once:!0})):t()}else n.removeEventListener("click",l.current);i.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",l.current)}},[n,r]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,n=[...E.branches].some(e=>e.contains(t));j&&!n&&(null==g||g(e),null==w||w(e),e.defaultPrevented||null==b||b())},R),D=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,u.c)(e),i=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!i.current&&h("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;![...E.branches].some(e=>e.contains(t))&&(null==y||y(e),null==w||w(e),e.defaultPrevented||null==b||b())},R);return!function(e,t=globalThis?.document){let n=(0,u.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{N===E.layers.size-1&&(null==m||m(e),!e.defaultPrevented&&b&&(e.preventDefault(),b()))},R),o.useEffect(()=>{if(S)return v&&(0===E.layersWithOutsidePointerEventsDisabled.size&&(r=R.body.style.pointerEvents,R.body.style.pointerEvents="none"),E.layersWithOutsidePointerEventsDisabled.add(S)),E.layers.add(S),p(),()=>{v&&1===E.layersWithOutsidePointerEventsDisabled.size&&(R.body.style.pointerEvents=r)}},[S,R,v,E]),o.useEffect(()=>()=>{S&&(E.layers.delete(S),E.layersWithOutsidePointerEventsDisabled.delete(S),p())},[S,E]),o.useEffect(()=>{let e=()=>A({});return document.addEventListener(s,e),()=>document.removeEventListener(s,e)},[]),(0,c.jsx)(l.sG.div,{...x,ref:T,style:{pointerEvents:M?j?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.m)(e.onFocusCapture,D.onFocusCapture),onBlurCapture:(0,i.m)(e.onBlurCapture,D.onBlurCapture),onPointerDownCapture:(0,i.m)(e.onPointerDownCapture,O.onPointerDownCapture)})});function p(){let e=new CustomEvent(s);document.dispatchEvent(e)}function h(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),o?(0,l.hO)(i,a):i.dispatchEvent(a)}f.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(d),r=o.useRef(null),i=(0,a.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,c.jsx)(l.sG.div,{...e,ref:i})}).displayName="DismissableLayerBranch"},9946:(e,t,n)=>{n.d(t,{A:()=>d});var r=n(2115);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,n)=>n?n.toUpperCase():t.toLowerCase()),l=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},a=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim()},u=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let s=(0,r.forwardRef)((e,t)=>{let{color:n="currentColor",size:o=24,strokeWidth:i=2,absoluteStrokeWidth:l,className:s="",children:d,iconNode:f,...p}=e;return(0,r.createElement)("svg",{ref:t,...c,width:o,height:o,stroke:n,strokeWidth:l?24*Number(i)/Number(o):i,className:a("lucide",s),...!d&&!u(p)&&{"aria-hidden":"true"},...p},[...f.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(d)?d:[d]])}),d=(e,t)=>{let n=(0,r.forwardRef)((n,i)=>{let{className:u,...c}=n;return(0,r.createElement)(s,{ref:i,iconNode:t,className:a("lucide-".concat(o(l(e))),"lucide-".concat(e),u),...c})});return n.displayName=l(e),n}},9963:(e,t,n)=>{n.d(t,{UC:()=>eW,In:()=>eD,q7:()=>eF,VF:()=>e_,p4:()=>eH,ZL:()=>eI,bL:()=>eM,wn:()=>eV,PP:()=>ez,l9:()=>ej,WT:()=>eO,LM:()=>eB});var r,o=n(2115),i=n(7650);function l(e,[t,n]){return Math.min(n,Math.max(t,e))}var a=n(5185);function u(e,t,n){if(!t.has(e))throw TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function c(e,t){var n=u(e,t,"get");return n.get?n.get.call(e):n.value}function s(e,t,n){var r=u(e,t,"set");if(r.set)r.set.call(e,n);else{if(!r.writable)throw TypeError("attempted to set read only private field");r.value=n}return n}var d=n(6081),f=n(6101),p=n(9708),h=n(5155),v=new WeakMap;function m(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=g(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return -1===n?void 0:e[n]}function g(e){return e!=e||0===e?0:Math.trunc(e)}r=new WeakMap;var y=o.createContext(void 0),w=n(9178),b=n(2293),x=n(7900),E=n(1285),S=n(8795),C=n(4378),R=n(3655),A=n(9033),T=n(5845),L=n(2712),k=n(2564),P=n(8168),N=n(3795),M=[" ","Enter","ArrowUp","ArrowDown"],j=[" ","Enter"],O="Select",[D,I,W]=function(e){let t=e+"CollectionProvider",[n,r]=(0,d.A)(t),[i,l]=n(t,{collectionRef:{current:null},itemMap:new Map}),a=e=>{let{scope:t,children:n}=e,r=o.useRef(null),l=o.useRef(new Map).current;return(0,h.jsx)(i,{scope:t,itemMap:l,collectionRef:r,children:n})};a.displayName=t;let u=e+"CollectionSlot",c=(0,p.TL)(u),s=o.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=l(u,n),i=(0,f.s)(t,o.collectionRef);return(0,h.jsx)(c,{ref:i,children:r})});s.displayName=u;let v=e+"CollectionItemSlot",m="data-radix-collection-item",g=(0,p.TL)(v),y=o.forwardRef((e,t)=>{let{scope:n,children:r,...i}=e,a=o.useRef(null),u=(0,f.s)(t,a),c=l(v,n);return o.useEffect(()=>(c.itemMap.set(a,{ref:a,...i}),()=>void c.itemMap.delete(a))),(0,h.jsx)(g,{...{[m]:""},ref:u,children:r})});return y.displayName=v,[{Provider:a,Slot:s,ItemSlot:y},function(t){let n=l(e+"CollectionConsumer",t);return o.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(m,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}(O),[B,F]=(0,d.A)(O,[W,S.Bk]),H=(0,S.Bk)(),[_,z]=B(O),[V,G]=B(O),K=e=>{let{__scopeSelect:t,children:n,open:r,defaultOpen:i,onOpenChange:l,value:a,defaultValue:u,onValueChange:c,dir:s,name:d,autoComplete:f,disabled:p,required:v,form:m}=e,g=H(t),[w,b]=o.useState(null),[x,C]=o.useState(null),[R,A]=o.useState(!1),L=function(e){let t=o.useContext(y);return e||t||"ltr"}(s),[k,P]=(0,T.i)({prop:r,defaultProp:null!=i&&i,onChange:l,caller:O}),[N,M]=(0,T.i)({prop:a,defaultProp:u,onChange:c,caller:O}),j=o.useRef(null),I=!w||m||!!w.closest("form"),[W,B]=o.useState(new Set),F=Array.from(W).map(e=>e.props.value).join(";");return(0,h.jsx)(S.bL,{...g,children:(0,h.jsxs)(_,{required:v,scope:t,trigger:w,onTriggerChange:b,valueNode:x,onValueNodeChange:C,valueNodeHasChildren:R,onValueNodeHasChildrenChange:A,contentId:(0,E.B)(),value:N,onValueChange:M,open:k,onOpenChange:P,dir:L,triggerPointerDownPosRef:j,disabled:p,children:[(0,h.jsx)(D.Provider,{scope:t,children:(0,h.jsx)(V,{scope:e.__scopeSelect,onNativeOptionAdd:o.useCallback(e=>{B(t=>new Set(t).add(e))},[]),onNativeOptionRemove:o.useCallback(e=>{B(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),I?(0,h.jsxs)(eL,{"aria-hidden":!0,required:v,tabIndex:-1,name:d,autoComplete:f,value:N,onChange:e=>M(e.target.value),disabled:p,form:m,children:[void 0===N?(0,h.jsx)("option",{value:""}):null,Array.from(W)]},F):null]})})};K.displayName=O;var $="SelectTrigger",U=o.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:r=!1,...i}=e,l=H(n),u=z($,n),c=u.disabled||r,s=(0,f.s)(t,u.onTriggerChange),d=I(n),p=o.useRef("touch"),[v,m,g]=eP(e=>{let t=d().filter(e=>!e.disabled),n=t.find(e=>e.value===u.value),r=eN(t,e,n);void 0!==r&&u.onValueChange(r.value)}),y=e=>{c||(u.onOpenChange(!0),g()),e&&(u.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,h.jsx)(S.Mz,{asChild:!0,...l,children:(0,h.jsx)(R.sG.button,{type:"button",role:"combobox","aria-controls":u.contentId,"aria-expanded":u.open,"aria-required":u.required,"aria-autocomplete":"none",dir:u.dir,"data-state":u.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":ek(u.value)?"":void 0,...i,ref:s,onClick:(0,a.m)(i.onClick,e=>{e.currentTarget.focus(),"mouse"!==p.current&&y(e)}),onPointerDown:(0,a.m)(i.onPointerDown,e=>{p.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(y(e),e.preventDefault())}),onKeyDown:(0,a.m)(i.onKeyDown,e=>{let t=""!==v.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&M.includes(e.key)&&(y(),e.preventDefault())})})})});U.displayName=$;var q="SelectValue",Y=o.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:i,placeholder:l="",...a}=e,u=z(q,n),{onValueNodeHasChildrenChange:c}=u,s=void 0!==i,d=(0,f.s)(t,u.onValueNodeChange);return(0,L.N)(()=>{c(s)},[c,s]),(0,h.jsx)(R.sG.span,{...a,ref:d,style:{pointerEvents:"none"},children:ek(u.value)?(0,h.jsx)(h.Fragment,{children:l}):i})});Y.displayName=q;var X=o.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,h.jsx)(R.sG.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});X.displayName="SelectIcon";var Z=e=>(0,h.jsx)(C.Z,{asChild:!0,...e});Z.displayName="SelectPortal";var Q="SelectContent",J=o.forwardRef((e,t)=>{let n=z(Q,e.__scopeSelect),[r,l]=o.useState();return((0,L.N)(()=>{l(new DocumentFragment)},[]),n.open)?(0,h.jsx)(er,{...e,ref:t}):r?i.createPortal((0,h.jsx)(ee,{scope:e.__scopeSelect,children:(0,h.jsx)(D.Slot,{scope:e.__scopeSelect,children:(0,h.jsx)("div",{children:e.children})})}),r):null});J.displayName=Q;var[ee,et]=B(Q),en=(0,p.TL)("SelectContent.RemoveScroll"),er=o.forwardRef((e,t)=>{let{__scopeSelect:n,position:r="item-aligned",onCloseAutoFocus:i,onEscapeKeyDown:l,onPointerDownOutside:u,side:c,sideOffset:s,align:d,alignOffset:p,arrowPadding:v,collisionBoundary:m,collisionPadding:g,sticky:y,hideWhenDetached:E,avoidCollisions:S,...C}=e,R=z(Q,n),[A,T]=o.useState(null),[L,k]=o.useState(null),M=(0,f.s)(t,e=>T(e)),[j,O]=o.useState(null),[D,W]=o.useState(null),B=I(n),[F,H]=o.useState(!1),_=o.useRef(!1);o.useEffect(()=>{if(A)return(0,P.Eq)(A)},[A]),(0,b.Oh)();let V=o.useCallback(e=>{let[t,...n]=B().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(null==n||n.scrollIntoView({block:"nearest"}),n===t&&L&&(L.scrollTop=0),n===r&&L&&(L.scrollTop=L.scrollHeight),null==n||n.focus(),document.activeElement!==o))return},[B,L]),G=o.useCallback(()=>V([j,A]),[V,j,A]);o.useEffect(()=>{F&&G()},[F,G]);let{onOpenChange:K,triggerPointerDownPosRef:$}=R;o.useEffect(()=>{if(A){let e={x:0,y:0},t=t=>{var n,r,o,i;e={x:Math.abs(Math.round(t.pageX)-(null!=(o=null==(n=$.current)?void 0:n.x)?o:0)),y:Math.abs(Math.round(t.pageY)-(null!=(i=null==(r=$.current)?void 0:r.y)?i:0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():A.contains(n.target)||K(!1),document.removeEventListener("pointermove",t),$.current=null};return null!==$.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[A,K,$]),o.useEffect(()=>{let e=()=>K(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[K]);let[U,q]=eP(e=>{let t=B().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=eN(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),Y=o.useCallback((e,t,n)=>{let r=!_.current&&!n;(void 0!==R.value&&R.value===t||r)&&(O(e),r&&(_.current=!0))},[R.value]),X=o.useCallback(()=>null==A?void 0:A.focus(),[A]),Z=o.useCallback((e,t,n)=>{let r=!_.current&&!n;(void 0!==R.value&&R.value===t||r)&&W(e)},[R.value]),J="popper"===r?ei:eo,et=J===ei?{side:c,sideOffset:s,align:d,alignOffset:p,arrowPadding:v,collisionBoundary:m,collisionPadding:g,sticky:y,hideWhenDetached:E,avoidCollisions:S}:{};return(0,h.jsx)(ee,{scope:n,content:A,viewport:L,onViewportChange:k,itemRefCallback:Y,selectedItem:j,onItemLeave:X,itemTextRefCallback:Z,focusSelectedItem:G,selectedItemText:D,position:r,isPositioned:F,searchRef:U,children:(0,h.jsx)(N.A,{as:en,allowPinchZoom:!0,children:(0,h.jsx)(x.n,{asChild:!0,trapped:R.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,a.m)(i,e=>{var t;null==(t=R.trigger)||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,h.jsx)(w.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:l,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>R.onOpenChange(!1),children:(0,h.jsx)(J,{role:"listbox",id:R.contentId,"data-state":R.open?"open":"closed",dir:R.dir,onContextMenu:e=>e.preventDefault(),...C,...et,onPlaced:()=>H(!0),ref:M,style:{display:"flex",flexDirection:"column",outline:"none",...C.style},onKeyDown:(0,a.m)(C.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||q(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=B().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>V(t)),e.preventDefault()}})})})})})})});er.displayName="SelectContentImpl";var eo=o.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:r,...i}=e,a=z(Q,n),u=et(Q,n),[c,s]=o.useState(null),[d,p]=o.useState(null),v=(0,f.s)(t,e=>p(e)),m=I(n),g=o.useRef(!1),y=o.useRef(!0),{viewport:w,selectedItem:b,selectedItemText:x,focusSelectedItem:E}=u,S=o.useCallback(()=>{if(a.trigger&&a.valueNode&&c&&d&&w&&b&&x){let e=a.trigger.getBoundingClientRect(),t=d.getBoundingClientRect(),n=a.valueNode.getBoundingClientRect(),o=x.getBoundingClientRect();if("rtl"!==a.dir){let r=o.left-t.left,i=n.left-r,a=e.left-i,u=e.width+a,s=Math.max(u,t.width),d=l(i,[10,Math.max(10,window.innerWidth-10-s)]);c.style.minWidth=u+"px",c.style.left=d+"px"}else{let r=t.right-o.right,i=window.innerWidth-n.right-r,a=window.innerWidth-e.right-i,u=e.width+a,s=Math.max(u,t.width),d=l(i,[10,Math.max(10,window.innerWidth-10-s)]);c.style.minWidth=u+"px",c.style.right=d+"px"}let i=m(),u=window.innerHeight-20,s=w.scrollHeight,f=window.getComputedStyle(d),p=parseInt(f.borderTopWidth,10),h=parseInt(f.paddingTop,10),v=parseInt(f.borderBottomWidth,10),y=p+h+s+parseInt(f.paddingBottom,10)+v,E=Math.min(5*b.offsetHeight,y),S=window.getComputedStyle(w),C=parseInt(S.paddingTop,10),R=parseInt(S.paddingBottom,10),A=e.top+e.height/2-10,T=b.offsetHeight/2,L=p+h+(b.offsetTop+T);if(L<=A){let e=i.length>0&&b===i[i.length-1].ref.current;c.style.bottom="0px";let t=Math.max(u-A,T+(e?R:0)+(d.clientHeight-w.offsetTop-w.offsetHeight)+v);c.style.height=L+t+"px"}else{let e=i.length>0&&b===i[0].ref.current;c.style.top="0px";let t=Math.max(A,p+w.offsetTop+(e?C:0)+T);c.style.height=t+(y-L)+"px",w.scrollTop=L-A+w.offsetTop}c.style.margin="".concat(10,"px 0"),c.style.minHeight=E+"px",c.style.maxHeight=u+"px",null==r||r(),requestAnimationFrame(()=>g.current=!0)}},[m,a.trigger,a.valueNode,c,d,w,b,x,a.dir,r]);(0,L.N)(()=>S(),[S]);let[C,A]=o.useState();(0,L.N)(()=>{d&&A(window.getComputedStyle(d).zIndex)},[d]);let T=o.useCallback(e=>{e&&!0===y.current&&(S(),null==E||E(),y.current=!1)},[S,E]);return(0,h.jsx)(el,{scope:n,contentWrapper:c,shouldExpandOnScrollRef:g,onScrollButtonChange:T,children:(0,h.jsx)("div",{ref:s,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:C},children:(0,h.jsx)(R.sG.div,{...i,ref:v,style:{boxSizing:"border-box",maxHeight:"100%",...i.style}})})})});eo.displayName="SelectItemAlignedPosition";var ei=o.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...i}=e,l=H(n);return(0,h.jsx)(S.UC,{...l,...i,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});ei.displayName="SelectPopperPosition";var[el,ea]=B(Q,{}),eu="SelectViewport",ec=o.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:r,...i}=e,l=et(eu,n),u=ea(eu,n),c=(0,f.s)(t,l.onViewportChange),s=o.useRef(0);return(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),(0,h.jsx)(D.Slot,{scope:n,children:(0,h.jsx)(R.sG.div,{"data-radix-select-viewport":"",role:"presentation",...i,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...i.style},onScroll:(0,a.m)(i.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=u;if((null==r?void 0:r.current)&&n){let e=Math.abs(s.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let i=o+e,l=Math.min(r,i),a=i-l;n.style.height=l+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}s.current=t.scrollTop})})})]})});ec.displayName=eu;var es="SelectGroup",[ed,ef]=B(es);o.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=(0,E.B)();return(0,h.jsx)(ed,{scope:n,id:o,children:(0,h.jsx)(R.sG.div,{role:"group","aria-labelledby":o,...r,ref:t})})}).displayName=es;var ep="SelectLabel";o.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=ef(ep,n);return(0,h.jsx)(R.sG.div,{id:o.id,...r,ref:t})}).displayName=ep;var eh="SelectItem",[ev,em]=B(eh),eg=o.forwardRef((e,t)=>{let{__scopeSelect:n,value:r,disabled:i=!1,textValue:l,...u}=e,c=z(eh,n),s=et(eh,n),d=c.value===r,[p,v]=o.useState(null!=l?l:""),[m,g]=o.useState(!1),y=(0,f.s)(t,e=>{var t;return null==(t=s.itemRefCallback)?void 0:t.call(s,e,r,i)}),w=(0,E.B)(),b=o.useRef("touch"),x=()=>{i||(c.onValueChange(r),c.onOpenChange(!1))};if(""===r)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,h.jsx)(ev,{scope:n,value:r,disabled:i,textId:w,isSelected:d,onItemTextChange:o.useCallback(e=>{v(t=>{var n;return t||(null!=(n=null==e?void 0:e.textContent)?n:"").trim()})},[]),children:(0,h.jsx)(D.ItemSlot,{scope:n,value:r,disabled:i,textValue:p,children:(0,h.jsx)(R.sG.div,{role:"option","aria-labelledby":w,"data-highlighted":m?"":void 0,"aria-selected":d&&m,"data-state":d?"checked":"unchecked","aria-disabled":i||void 0,"data-disabled":i?"":void 0,tabIndex:i?void 0:-1,...u,ref:y,onFocus:(0,a.m)(u.onFocus,()=>g(!0)),onBlur:(0,a.m)(u.onBlur,()=>g(!1)),onClick:(0,a.m)(u.onClick,()=>{"mouse"!==b.current&&x()}),onPointerUp:(0,a.m)(u.onPointerUp,()=>{"mouse"===b.current&&x()}),onPointerDown:(0,a.m)(u.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,a.m)(u.onPointerMove,e=>{if(b.current=e.pointerType,i){var t;null==(t=s.onItemLeave)||t.call(s)}else"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.m)(u.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null==(t=s.onItemLeave)||t.call(s)}}),onKeyDown:(0,a.m)(u.onKeyDown,e=>{var t;((null==(t=s.searchRef)?void 0:t.current)===""||" "!==e.key)&&(j.includes(e.key)&&x()," "===e.key&&e.preventDefault())})})})})});eg.displayName=eh;var ey="SelectItemText",ew=o.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:l,...a}=e,u=z(ey,n),c=et(ey,n),s=em(ey,n),d=G(ey,n),[p,v]=o.useState(null),m=(0,f.s)(t,e=>v(e),s.onItemTextChange,e=>{var t;return null==(t=c.itemTextRefCallback)?void 0:t.call(c,e,s.value,s.disabled)}),g=null==p?void 0:p.textContent,y=o.useMemo(()=>(0,h.jsx)("option",{value:s.value,disabled:s.disabled,children:g},s.value),[s.disabled,s.value,g]),{onNativeOptionAdd:w,onNativeOptionRemove:b}=d;return(0,L.N)(()=>(w(y),()=>b(y)),[w,b,y]),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(R.sG.span,{id:s.textId,...a,ref:m}),s.isSelected&&u.valueNode&&!u.valueNodeHasChildren?i.createPortal(a.children,u.valueNode):null]})});ew.displayName=ey;var eb="SelectItemIndicator",ex=o.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return em(eb,n).isSelected?(0,h.jsx)(R.sG.span,{"aria-hidden":!0,...r,ref:t}):null});ex.displayName=eb;var eE="SelectScrollUpButton",eS=o.forwardRef((e,t)=>{let n=et(eE,e.__scopeSelect),r=ea(eE,e.__scopeSelect),[i,l]=o.useState(!1),a=(0,f.s)(t,r.onScrollButtonChange);return(0,L.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){l(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,h.jsx)(eA,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});eS.displayName=eE;var eC="SelectScrollDownButton",eR=o.forwardRef((e,t)=>{let n=et(eC,e.__scopeSelect),r=ea(eC,e.__scopeSelect),[i,l]=o.useState(!1),a=(0,f.s)(t,r.onScrollButtonChange);return(0,L.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;l(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,h.jsx)(eA,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eR.displayName=eC;var eA=o.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:r,...i}=e,l=et("SelectScrollButton",n),u=o.useRef(null),c=I(n),s=o.useCallback(()=>{null!==u.current&&(window.clearInterval(u.current),u.current=null)},[]);return o.useEffect(()=>()=>s(),[s]),(0,L.N)(()=>{var e;let t=c().find(e=>e.ref.current===document.activeElement);null==t||null==(e=t.ref.current)||e.scrollIntoView({block:"nearest"})},[c]),(0,h.jsx)(R.sG.div,{"aria-hidden":!0,...i,ref:t,style:{flexShrink:0,...i.style},onPointerDown:(0,a.m)(i.onPointerDown,()=>{null===u.current&&(u.current=window.setInterval(r,50))}),onPointerMove:(0,a.m)(i.onPointerMove,()=>{var e;null==(e=l.onItemLeave)||e.call(l),null===u.current&&(u.current=window.setInterval(r,50))}),onPointerLeave:(0,a.m)(i.onPointerLeave,()=>{s()})})});o.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,h.jsx)(R.sG.div,{"aria-hidden":!0,...r,ref:t})}).displayName="SelectSeparator";var eT="SelectArrow";o.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=H(n),i=z(eT,n),l=et(eT,n);return i.open&&"popper"===l.position?(0,h.jsx)(S.i3,{...o,...r,ref:t}):null}).displayName=eT;var eL=o.forwardRef((e,t)=>{let{__scopeSelect:n,value:r,...i}=e,l=o.useRef(null),a=(0,f.s)(t,l),u=function(e){let t=o.useRef({value:e,previous:e});return o.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(r);return o.useEffect(()=>{let e=l.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(u!==r&&t){let n=new Event("change",{bubbles:!0});t.call(e,r),e.dispatchEvent(n)}},[u,r]),(0,h.jsx)(R.sG.select,{...i,style:{...k.Qg,...i.style},ref:a,defaultValue:r})});function ek(e){return""===e||void 0===e}function eP(e){let t=(0,A.c)(e),n=o.useRef(""),r=o.useRef(0),i=o.useCallback(e=>{let o=n.current+e;t(o),function e(t){n.current=t,window.clearTimeout(r.current),""!==t&&(r.current=window.setTimeout(()=>e(""),1e3))}(o)},[t]),l=o.useCallback(()=>{n.current="",window.clearTimeout(r.current)},[]);return o.useEffect(()=>()=>window.clearTimeout(r.current),[]),[n,i,l]}function eN(e,t,n){var r,o;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,l=n?e.indexOf(n):-1,a=(r=e,o=Math.max(l,0),r.map((e,t)=>r[(o+t)%r.length]));1===i.length&&(a=a.filter(e=>e!==n));let u=a.find(e=>e.textValue.toLowerCase().startsWith(i.toLowerCase()));return u!==n?u:void 0}eL.displayName="SelectBubbleInput";var eM=K,ej=U,eO=Y,eD=X,eI=Z,eW=J,eB=ec,eF=eg,eH=ew,e_=ex,ez=eS,eV=eR}}]);