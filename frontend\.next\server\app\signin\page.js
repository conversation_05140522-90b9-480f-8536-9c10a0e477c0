(()=>{var e={};e.id=217,e.ids=[217],e.modules={440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});var s=t(1658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},698:(e,r,t)=>{Promise.resolve().then(t.bind(t,5029))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},866:(e,r,t)=>{Promise.resolve().then(t.bind(t,9615))},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1820:e=>{"use strict";e.exports=require("os")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},3997:e=>{"use strict";e.exports=require("tty")},4075:e=>{"use strict";e.exports=require("zlib")},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},5029:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\app\\\\signin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\AI-Trainer3\\frontend\\src\\app\\signin\\page.tsx","default")},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},6091:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=t(5239),n=t(8088),i=t(8170),a=t.n(i),o=t(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let d={children:["",{children:["signin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,5029)),"C:\\Projects\\AI-Trainer3\\frontend\\src\\app\\signin\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,8429)),"C:\\Projects\\AI-Trainer3\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Projects\\AI-Trainer3\\frontend\\src\\app\\signin\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/signin/page",pathname:"/signin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},7910:e=>{"use strict";e.exports=require("stream")},8354:e=>{"use strict";e.exports=require("util")},9021:e=>{"use strict";e.exports=require("fs")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9551:e=>{"use strict";e.exports=require("url")},9615:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(687),n=t(3210),i=t(6189);function a(){let[e,r]=(0,n.useState)(""),[t,a]=(0,n.useState)(""),[o,l]=(0,n.useState)(""),[d,c]=(0,n.useState)(!1),u=(0,i.useRouter)(),p=async r=>{r.preventDefault(),c(!0),l("");try{let r=await fetch("http://172.24.175.70:5001/api/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t})}),s=await r.json();if(!r.ok)throw Error(s.error||"Login failed");localStorage.setItem("token",s.token),"admin"===s.user.role?u.push("/admin"):u.push("/courses")}catch(e){l(e.message)}finally{c(!1)}};return(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-screen bg-[#fafbfc]",children:[(0,s.jsxs)("div",{className:"flex flex-col items-center mb-8",children:[(0,s.jsx)("div",{className:"bg-orange-500 rounded-lg p-3 mb-4",children:(0,s.jsx)("svg",{width:"32",height:"32",fill:"white",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z"})})}),(0,s.jsx)("h1",{className:"text-3xl font-bold mb-2",children:"Welcome back"}),(0,s.jsx)("p",{className:"text-gray-500",children:"Sign in to your AI Trainer account"})]}),(0,s.jsxs)("form",{onSubmit:p,className:"bg-white p-8 rounded-lg shadow-md w-full max-w-md",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Sign In"}),(0,s.jsx)("p",{className:"text-gray-500 mb-4",children:"Enter your email and password to access your account"}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block mb-1 font-medium",children:"Email"}),(0,s.jsx)("input",{type:"email",className:"w-full border rounded px-3 py-2",value:e,onChange:e=>r(e.target.value),required:!0,placeholder:"Enter your email"})]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block mb-1 font-medium",children:"Password"}),(0,s.jsx)("input",{type:"password",className:"w-full border rounded px-3 py-2",value:t,onChange:e=>a(e.target.value),required:!0,placeholder:"Enter your password"})]}),o&&(0,s.jsx)("div",{className:"text-red-500 mb-2",children:o}),(0,s.jsx)("button",{type:"submit",className:"w-full bg-orange-500 text-white py-2 rounded mt-2 hover:bg-orange-600 transition",disabled:d,children:d?"Signing In...":"Sign In"}),(0,s.jsxs)("div",{className:"text-center mt-4 text-gray-600",children:["Don't have an account? ",(0,s.jsx)("a",{href:"/signup",className:"text-orange-500 hover:underline",children:"Sign up"})]})]})]})}},9646:e=>{"use strict";e.exports=require("child_process")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,773,658,578],()=>t(6091));module.exports=s})();