(()=>{var e={};e.id=426,e.ids=[426],e.modules={440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(1658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1820:e=>{"use strict";e.exports=require("os")},2952:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(687),a=s(3210),i=s(9190);let n=()=>{let[e,t]=(0,a.useState)(""),[s,n]=(0,a.useState)(!1),[o,l]=(0,a.useState)(""),c=async()=>{if(!e)return void l("Please enter a course ID");n(!0),l("");try{let t=localStorage.getItem("token");if(!t){l("Please login first"),n(!1);return}let s=await fetch("http://172.24.175.70:5001/api/test/publish-video",{method:"POST",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"},body:JSON.stringify({courseId:parseInt(e)})}),r=await s.json();s.ok?l(`Success: ${r.message}`):l(`Error: ${r.error}`)}catch(e){l(`Error: ${e}`)}finally{n(!1)}},d=async()=>{n(!0),l("");try{let e=localStorage.getItem("token");if(!e){l("Please login first"),n(!1);return}let t=await fetch("http://172.24.175.70:5001/api/notifications/check",{method:"POST",headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}}),s=await t.json();t.ok?l(`Success: ${s.message}`):l(`Error: ${s.error}`)}catch(e){l(`Error: ${e}`)}finally{n(!1)}};return(0,r.jsxs)("div",{className:"flex flex-col min-h-screen bg-[#fafbfc]",children:[(0,r.jsx)(i.default,{}),(0,r.jsx)("main",{className:"flex-grow container mx-auto px-4 py-8",children:(0,r.jsxs)("div",{className:"max-w-2xl mx-auto",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-800 mb-8",children:"Test Notification System"}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Simulate Video Publishing"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:'This will update a video\'s status to "published" and trigger a notification.'}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{htmlFor:"courseId",className:"block text-sm font-medium text-gray-700 mb-2",children:"Course ID:"}),(0,r.jsx)("input",{type:"number",id:"courseId",value:e,onChange:e=>t(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500",placeholder:"Enter course ID"})]}),(0,r.jsx)("button",{onClick:c,disabled:s,className:"bg-orange-500 text-white px-4 py-2 rounded-md hover:bg-orange-600 disabled:opacity-50 disabled:cursor-not-allowed",children:s?"Publishing...":"Publish Video"})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Manual Notification Check"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"Manually trigger the notification system to check for new published videos."}),(0,r.jsx)("button",{onClick:d,disabled:s,className:"bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed",children:s?"Checking...":"Trigger Check"})]}),o&&(0,r.jsx)("div",{className:`p-4 rounded-md ${o.startsWith("Success")?"bg-green-100 text-green-800 border border-green-200":"bg-red-100 text-red-800 border border-red-200"}`,children:o}),(0,r.jsxs)("div",{className:"bg-gray-100 rounded-lg p-6 mt-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-3",children:"Instructions:"}),(0,r.jsxs)("ol",{className:"list-decimal list-inside space-y-2 text-gray-700",children:[(0,r.jsx)("li",{children:"Make sure you're logged in"}),(0,r.jsx)("li",{children:'Enter a course ID that has videos in "draft" status'}),(0,r.jsx)("li",{children:'Click "Publish Video" to simulate publishing'}),(0,r.jsx)("li",{children:"Watch the notification bell in the navbar for real-time updates"}),(0,r.jsx)("li",{children:"You can also manually trigger a check to test the polling system"})]})]})]})})]})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},3997:e=>{"use strict";e.exports=require("tty")},4075:e=>{"use strict";e.exports=require("zlib")},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},5511:e=>{"use strict";e.exports=require("crypto")},5565:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>d,routeModule:()=>u,tree:()=>c});var r=s(5239),a=s(8088),i=s(8170),n=s.n(i),o=s(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let c={children:["",{children:["test-notifications",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,9542)),"C:\\Projects\\AI-Trainer3\\frontend\\src\\app\\test-notifications\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,8429)),"C:\\Projects\\AI-Trainer3\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Projects\\AI-Trainer3\\frontend\\src\\app\\test-notifications\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/test-notifications/page",pathname:"/test-notifications",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},5587:(e,t,s)=>{Promise.resolve().then(s.bind(s,2952))},5591:e=>{"use strict";e.exports=require("https")},7880:(e,t,s)=>{Promise.resolve().then(s.bind(s,9542))},7910:e=>{"use strict";e.exports=require("stream")},8354:e=>{"use strict";e.exports=require("util")},9021:e=>{"use strict";e.exports=require("fs")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9190:(e,t,s)=>{"use strict";s.d(t,{default:()=>c});var r=s(687),a=s(3210),i=s(5885),n=s(7e3),o=s(7346),l=s(7351);let c=()=>{let[e,t]=(0,a.useState)(!1),[s,c]=(0,a.useState)(null),d=(0,a.useRef)(null),x=e=>{try{let t=e.split(".")[1].replace(/-/g,"+").replace(/_/g,"/"),s=decodeURIComponent(atob(t).split("").map(function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)}).join(""));return JSON.parse(s)}catch(e){return console.error("Error decoding JWT:",e),null}};(0,a.useEffect)(()=>{let e=localStorage.getItem("token");if(e){let t=x(e);t&&c({id:t.id,email:t.email,username:t.username,role:t.role,first_name:t.first_name||t.username,last_name:t.last_name||""})}},[]);let u=(e,t)=>(e?e.charAt(0).toUpperCase():"")+(t?t.charAt(0).toUpperCase():"")||"U";return(0,a.useEffect)(()=>{let e=e=>{d.current&&!d.current.contains(e.target)&&t(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]),(0,r.jsxs)("nav",{className:"bg-white p-4 shadow-md",children:[(0,r.jsxs)("div",{className:"container mx-auto flex justify-between items-center",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"bg-orange-500 rounded-xl p-2 flex items-center justify-center rounded-lg",children:(0,r.jsx)(l.A,{className:"w-6 h-6 text-white"})}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"text-orange-500 text-lg font-bold",children:"EX"}),(0,r.jsx)("span",{className:"text-[#005071] text-lg font-bold",children:"Learn"})]})]}),(0,r.jsx)("div",{className:"flex-grow flex justify-center mx-4",children:(0,r.jsx)("div",{className:"relative w-full max-w-[280px]",children:(0,r.jsx)("input",{type:"text",placeholder:"Search courses, topics, or instructors...",className:"w-full pl-3 pr-3 py-1 rounded-full border border-transparent focus:outline-none focus:ring-1 focus:ring-orange-100 text-xs placeholder-gray-400"})})}),(0,r.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,r.jsx)(o.A,{}),(0,r.jsx)("div",{className:"relative",ref:d,children:s?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 cursor-pointer hover:bg-gray-50 rounded-lg px-2 py-1 transition-colors duration-200",onClick:()=>{t(!e)},children:[(0,r.jsx)("div",{className:"bg-orange-500 text-white rounded-full h-8 w-8 flex items-center justify-center font-medium",children:u(s.first_name,s.last_name)}),(0,r.jsx)("span",{className:"text-gray-700 text-sm font-medium",children:s.first_name}),(0,r.jsx)(i.g,{icon:n.Jt$,className:`text-gray-500 text-xs transition-transform duration-200 ${e?"rotate-180":""}`})]}),e&&(0,r.jsxs)("div",{className:"absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50",children:[(0,r.jsx)("div",{className:"px-4 py-3 border-b border-gray-100",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"bg-orange-500 text-white rounded-full h-10 w-10 flex items-center justify-center font-medium",children:u(s.first_name,s.last_name)}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{className:"text-sm font-medium text-gray-900",children:[s.first_name," ",s.last_name]}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:s.email})]})]})}),(0,r.jsxs)("div",{className:"py-1",children:[(0,r.jsxs)("button",{className:"w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200",onClick:()=>{t(!1)},children:[(0,r.jsx)(i.g,{icon:n.X46,className:"mr-3 text-gray-400"}),"Profile"]}),(0,r.jsxs)("button",{className:"w-full flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors duration-200",onClick:()=>{localStorage.removeItem("token"),sessionStorage.removeItem("token"),localStorage.removeItem("authToken"),sessionStorage.removeItem("authToken"),c(null),window.location.href="/signin"},children:[(0,r.jsx)(i.g,{icon:n.GxD,className:"mr-3 text-red-500"}),"Log out"]})]})]})]}):(0,r.jsxs)("a",{href:"/signin",className:"flex items-center space-x-2 bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors duration-200",children:[(0,r.jsx)(i.g,{icon:n.X46,className:"text-sm"}),(0,r.jsx)("span",{className:"text-sm font-medium",children:"Sign In"})]})})]})]}),(0,r.jsx)("div",{className:"container mx-auto mt-4",children:(0,r.jsxs)("ul",{className:"flex space-x-8",children:[(0,r.jsx)("li",{children:(0,r.jsxs)("a",{href:"/",className:"flex items-center space-x-2 text-orange-500 border-b-2 border-orange-500 pb-2",children:[(0,r.jsx)(i.g,{icon:n.v02,className:"text-sm"}),(0,r.jsx)("span",{children:"Home"})]})}),(0,r.jsx)("li",{children:(0,r.jsxs)("a",{href:"/my-dashboard",className:"flex items-center space-x-2 text-gray-700 hover:text-orange-500",children:[(0,r.jsx)(i.g,{icon:n.xiI,className:"text-sm"}),(0,r.jsx)("span",{children:"My Dashboard"})]})}),(0,r.jsx)("li",{children:(0,r.jsxs)("a",{href:"/my-learning",className:"flex items-center space-x-2 text-gray-700 hover:text-orange-500",children:[(0,r.jsx)(i.g,{icon:n.ReK,className:"text-sm"}),(0,r.jsx)("span",{children:"My Learning"})]})}),(0,r.jsx)("li",{children:(0,r.jsxs)("a",{href:"/my-certificates",className:"flex items-center space-x-2 text-gray-700 hover:text-orange-500",children:[(0,r.jsx)(i.g,{icon:n.fmL,className:"text-sm"}),(0,r.jsx)("span",{children:"Certificates"})]})})]})})]})}},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9542:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\app\\\\test-notifications\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\AI-Trainer3\\frontend\\src\\app\\test-notifications\\page.tsx","default")},9551:e=>{"use strict";e.exports=require("url")},9646:e=>{"use strict";e.exports=require("child_process")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,773,658,578],()=>s(5565));module.exports=r})();