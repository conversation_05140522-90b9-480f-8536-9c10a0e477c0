(()=>{var e={};e.id=856,e.ids=[856],e.modules={440:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>i});var t=s(1658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1820:e=>{"use strict";e.exports=require("os")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},3997:e=>{"use strict";e.exports=require("tty")},4075:e=>{"use strict";e.exports=require("zlib")},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},5372:(e,r,s)=>{Promise.resolve().then(s.bind(s,9472))},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},5884:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\app\\\\admin\\\\course-builder\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\AI-Trainer3\\frontend\\src\\app\\admin\\course-builder\\page.tsx","default")},7228:(e,r,s)=>{Promise.resolve().then(s.bind(s,5884))},7241:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>d});var t=s(5239),i=s(8088),a=s(8170),o=s.n(a),n=s(893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);s.d(r,l);let d={children:["",{children:["admin",{children:["course-builder",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,5884)),"C:\\Projects\\AI-Trainer3\\frontend\\src\\app\\admin\\course-builder\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,8429)),"C:\\Projects\\AI-Trainer3\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Projects\\AI-Trainer3\\frontend\\src\\app\\admin\\course-builder\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/admin/course-builder/page",pathname:"/admin/course-builder",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},7910:e=>{"use strict";e.exports=require("stream")},8354:e=>{"use strict";e.exports=require("util")},9021:e=>{"use strict";e.exports=require("fs")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9472:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>o});var t=s(687),i=s(3210);function a(){let[e,r]=(0,i.useState)([]),[s,a]=(0,i.useState)(!0),[o,n]=(0,i.useState)(""),[l,d]=(0,i.useState)({course_title:"",status:"",page_number:""}),[c,u]=(0,i.useState)(new Set),x=async()=>{try{a(!0);let e=new URLSearchParams;l.status&&e.append("status",l.status),l.page_number&&e.append("page_number",l.page_number);let s=await fetch(`http://*************:5001/api/video-generation-history?${e}`);if(!s.ok)throw Error("Failed to fetch video generation history");let t=await s.json(),i=t;l.course_title&&(i=t.filter(e=>e.course_title.toLowerCase().includes(l.course_title.toLowerCase()))),r(i),n("")}catch(e){n(e.message),console.error("Error fetching video history:",e)}finally{a(!1)}},h=e=>{d({...l,[e.target.name]:e.target.value})},p=async(e,r)=>{try{if(!(await fetch(`http://*************:5001/api/video-generation-history/${e}/publish-status`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({video_publish_status:"published"===r?"draft":"published"})})).ok)throw Error("Failed to update publish status");x()}catch(e){n(e.message),console.error("Error updating publish status:",e)}},m=async e=>{if(confirm("Are you sure you want to delete this video generation record?"))try{if(!(await fetch(`http://*************:5001/api/video-generation-history/${e}`,{method:"DELETE"})).ok)throw Error("Failed to delete record");x()}catch(e){n(e.message),console.error("Error deleting record:",e)}},g=e.reduce((e,r)=>{let s=r.course_id;return e[s]||(e[s]={course_id:s,course_title:r.course_title,course_description:r.course_description,records:[]}),e[s].records.push(r),e},{}),b=e=>{let r=new Set(c);r.has(e)?r.delete(e):r.add(e),u(r)},v=e=>(0,t.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${{success:"bg-green-100 text-green-800",failed:"bg-red-100 text-red-800",processing:"bg-yellow-100 text-yellow-800",skipped_empty_script:"bg-gray-100 text-gray-800"}[e]||"bg-gray-100 text-gray-800"}`,children:e.replace("_"," ").toUpperCase()}),f=e=>(0,t.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${{published:"bg-blue-100 text-blue-800",draft:"bg-orange-100 text-orange-800",archived:"bg-gray-100 text-gray-800"}[e]||"bg-gray-100 text-gray-800"}`,children:e.toUpperCase()});return(0,t.jsxs)("div",{className:"bg-white rounded-2xl shadow-lg p-8",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"bg-blue-100 p-3 rounded-xl",children:(0,t.jsxs)("svg",{className:"w-6 h-6 text-blue-600",fill:"none",stroke:"currentColor",strokeWidth:"2",viewBox:"0 0 24 24",children:[(0,t.jsx)("rect",{width:"18",height:"18",x:"3",y:"3",rx:"2"}),(0,t.jsx)("path",{d:"M9 3v18"}),(0,t.jsx)("path",{d:"M15 3v18"})]})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-800",children:"Course Builder"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Video Generation History & Course Management"})]})]}),(0,t.jsxs)("button",{onClick:x,disabled:s,className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:[(0,t.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",strokeWidth:"2",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Refresh"]})]}),(0,t.jsxs)("div",{className:"bg-gray-50 rounded-xl p-6 mb-6",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Filters"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Course Title"}),(0,t.jsx)("input",{type:"text",name:"course_title",value:l.course_title,onChange:h,placeholder:"Search by course title",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Status"}),(0,t.jsxs)("select",{name:"status",value:l.status,onChange:h,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)("option",{value:"",children:"All Statuses"}),(0,t.jsx)("option",{value:"success",children:"Success"}),(0,t.jsx)("option",{value:"failed",children:"Failed"}),(0,t.jsx)("option",{value:"processing",children:"Processing"}),(0,t.jsx)("option",{value:"skipped_empty_script",children:"Skipped"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Page Number"}),(0,t.jsx)("input",{type:"number",name:"page_number",value:l.page_number,onChange:h,placeholder:"Enter page number",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,t.jsxs)("div",{className:"flex items-end gap-2",children:[(0,t.jsx)("button",{onClick:()=>{x()},className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Apply"}),(0,t.jsx)("button",{onClick:()=>{d({course_title:"",status:"",page_number:""}),setTimeout(()=>{x()},100)},className:"px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors",children:"Clear"})]})]})]}),s&&(0,t.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),(0,t.jsx)("span",{className:"ml-3 text-gray-600",children:"Loading video generation history..."})]}),o&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("svg",{className:"w-5 h-5 text-red-500 mr-2",fill:"none",stroke:"currentColor",strokeWidth:"2",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})}),(0,t.jsxs)("span",{className:"text-red-700 font-medium",children:["Error: ",o]})]})}),!s&&!o&&(0,t.jsx)("div",{className:"mb-4",children:(0,t.jsxs)("p",{className:"text-gray-600",children:["Showing ",Object.keys(g).length," course",1!==Object.keys(g).length?"s":""," with ",e.length," video generation record",1!==e.length?"s":""]})}),!s&&!o&&Object.keys(g).length>0&&(0,t.jsx)("div",{className:"space-y-4",children:Object.values(g).map(e=>(0,t.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden",children:[(0,t.jsx)("div",{className:"p-6 cursor-pointer hover:bg-gray-50 transition-colors",onClick:()=>b(e.course_id),children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:`transform transition-transform ${c.has(e.course_id)?"rotate-90":""}`,children:(0,t.jsx)("svg",{className:"w-5 h-5 text-gray-400",fill:"none",stroke:"currentColor",strokeWidth:"2",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M9 5l7 7-7 7"})})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:e.course_title}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.course_description&&e.course_description.length>120?`${e.course_description.substring(0,120)}...`:e.course_description})]})]})}),(0,t.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-500",children:[(0,t.jsxs)("span",{className:"bg-blue-100 text-blue-800 px-3 py-1 rounded-full font-medium",children:[e.records.length," page",1!==e.records.length?"s":""]}),(0,t.jsxs)("span",{children:["ID: ",e.course_id]})]})]})}),c.has(e.course_id)&&(0,t.jsx)("div",{className:"border-t border-gray-100",children:e.records.map((r,s)=>(0,t.jsxs)("div",{className:`p-6 ${s!==e.records.length-1?"border-b border-gray-50":""}`,children:[(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4 mb-3",children:[(0,t.jsxs)("span",{className:"bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm font-medium",children:["\uD83D\uDCC4 Page ",r.page_number]}),(0,t.jsx)("span",{className:"text-sm font-medium text-gray-900",children:r.page_title})]}),(0,t.jsxs)("div",{className:"flex items-center gap-6 text-sm text-gray-500 mb-3",children:[(0,t.jsxs)("span",{children:["\uD83C\uDFAD ",r.avatar_name]}),(0,t.jsxs)("span",{children:["\uD83D\uDCC5 ",new Date(r.created_at).toLocaleDateString()]}),(0,t.jsxs)("span",{children:["\uD83C\uDD94 Version: ",r.version_number]}),r.video_id&&(0,t.jsxs)("span",{children:["\uD83C\uDFAC Video ID: ",r.video_id]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[v(r.generation_status),f(r.video_publish_status)]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2 ml-4",children:[r.heygenvideourl&&(0,t.jsx)("button",{onClick:()=>window.open(r.heygenvideourl,"_blank"),className:"p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors",title:"View Video",children:(0,t.jsxs)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",strokeWidth:"2",viewBox:"0 0 24 24",children:[(0,t.jsx)("path",{d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),(0,t.jsx)("path",{d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]})}),(0,t.jsx)("button",{onClick:()=>p(r.id,r.video_publish_status),className:`p-2 rounded-lg transition-colors ${"published"===r.video_publish_status?"text-green-600 hover:text-green-700 hover:bg-green-50":"text-gray-400 hover:text-green-600 hover:bg-green-50"}`,title:"published"===r.video_publish_status?"Unpublish":"Publish",children:(0,t.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",strokeWidth:"2",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M12 19l9 2-9-18-9 18 9-2zm0 0v-8"})})}),(0,t.jsx)("button",{onClick:()=>m(r.id),className:"p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors",title:"Delete Record",children:(0,t.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",strokeWidth:"2",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]})]}),r.heygenbloburl&&(0,t.jsx)("div",{className:"mt-3 pt-3 border-t border-gray-100",children:(0,t.jsxs)("a",{href:r.heygenbloburl,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center gap-2 text-blue-600 hover:text-blue-800 font-medium text-sm",children:[(0,t.jsxs)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",strokeWidth:"2",viewBox:"0 0 24 24",children:[(0,t.jsx)("path",{d:"M4 16v2a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-2"}),(0,t.jsx)("polyline",{points:"8 12 12 16 16 12"}),(0,t.jsx)("line",{x1:"12",y1:"8",x2:"12",y2:"16"})]}),"Download Video"]})})]},r.id))})]},e.course_id))}),!s&&!o&&0===e.length&&(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)("svg",{className:"w-16 h-16 text-gray-400 mx-auto mb-4",fill:"none",stroke:"currentColor",strokeWidth:"1",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No video generation history found"}),(0,t.jsx)("p",{className:"text-gray-500",children:"Try adjusting your filters or generate some videos first."})]})]})}function o(){return(0,t.jsx)(a,{})}},9551:e=>{"use strict";e.exports=require("url")},9646:e=>{"use strict";e.exports=require("child_process")}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[447,773,658,578],()=>s(7241));module.exports=t})();