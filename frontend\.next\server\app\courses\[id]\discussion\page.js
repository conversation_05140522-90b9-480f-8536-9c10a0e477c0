(()=>{var e={};e.id=152,e.ids=[152],e.modules={80:(e,s,t)=>{"use strict";async function r(){try{return["1","2","3","4","5","46","47","48","49","50"].map(e=>({id:e}))}catch(e){return console.warn("Error generating static params:",e),[]}}function a({children:e}){return e}t.r(s),t.d(s,{default:()=>a,generateStaticParams:()=>r})},440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(1658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},759:(e,s,t)=>{Promise.resolve().then(t.bind(t,7471))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1820:e=>{"use strict";e.exports=require("os")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3807:(e,s,t)=>{Promise.resolve().then(t.bind(t,9690))},3873:e=>{"use strict";e.exports=require("path")},3997:e=>{"use strict";e.exports=require("tty")},4075:e=>{"use strict";e.exports=require("zlib")},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},5817:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>d,routeModule:()=>m,tree:()=>c});var r=t(5239),a=t(8088),i=t(8170),n=t.n(i),l=t(893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let c={children:["",{children:["courses",{children:["[id]",{children:["discussion",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,9690)),"C:\\Projects\\AI-Trainer3\\frontend\\src\\app\\courses\\[id]\\discussion\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,80)),"C:\\Projects\\AI-Trainer3\\frontend\\src\\app\\courses\\[id]\\layout.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,8429)),"C:\\Projects\\AI-Trainer3\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Projects\\AI-Trainer3\\frontend\\src\\app\\courses\\[id]\\discussion\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/courses/[id]/discussion/page",pathname:"/courses/[id]/discussion",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},6487:()=>{},7471:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d});var r=t(687),a=t(3210),i=t(6189),n=t(9190),l=t(5885),o=t(7e3);function c({courseId:e,userId:s}){let[t,i]=(0,a.useState)([]),[n,c]=(0,a.useState)(null),[d,x]=(0,a.useState)([]),[m,u]=(0,a.useState)(!0),[h,g]=(0,a.useState)(!1),[f,p]=(0,a.useState)(!1),[j,b]=(0,a.useState)(""),[y,v]=(0,a.useState)(!1),[N,w]=(0,a.useState)(""),[k,C]=(0,a.useState)(""),[_,S]=(0,a.useState)("");(0,a.useCallback)(async()=>{try{let s=await fetch(`http://172.24.175.70:5001/api/courses/${e}/discussions`);if(s.ok){let e=await s.json();i(e)}}catch(e){console.error("Error fetching discussions:",e)}finally{u(!1)}},[e]);let P=(0,a.useCallback)(async e=>{try{let s=await fetch(`http://172.24.175.70:5001/api/discussions/${e}/replies`);if(s.ok){let e=await s.json();x(e)}}catch(e){console.error("Error fetching replies:",e)}},[]),q=async r=>{if(r.preventDefault(),N.trim()&&k.trim()&&s){v(!0);try{let r=await fetch(`http://172.24.175.70:5001/api/courses/${e}/discussions`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:s,title:N,content:k})});if(r.ok){let e=await r.json();i([e,...t]),w(""),C(""),g(!1)}}catch(e){console.error("Error creating discussion:",e)}finally{v(!1)}}},A=async e=>{if(e.preventDefault(),_.trim()&&n&&s){v(!0);try{let e=await fetch(`http://172.24.175.70:5001/api/discussions/${n.id}/replies`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:s,content:_})});if(e.ok){let s=await e.json();x([...d,s]),S(""),p(!1),i(t.map(e=>e.id===n.id?{...e,reply_count:e.reply_count+1}:e))}}catch(e){console.error("Error creating reply:",e)}finally{v(!1)}}},E=e=>{let s=new Date(e),t=Math.floor((new Date().getTime()-s.getTime())/6e4);return t<1?"Just now":t<60?`${t}m ago`:t<1440?`${Math.floor(t/60)}h ago`:t<10080?`${Math.floor(t/1440)}d ago`:s.toLocaleDateString()},L=(e,s)=>`${e.charAt(0)}${s.charAt(0)}`.toUpperCase(),I=t.filter(e=>e.title.toLowerCase().includes(j.toLowerCase())||e.content.toLowerCase().includes(j.toLowerCase())||`${e.first_name} ${e.last_name}`.toLowerCase().includes(j.toLowerCase())),D=e=>{c(e),p(!1),P(e.id)};return s?m?(0,r.jsx)("div",{className:"flex justify-center items-center py-12",children:(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"}),(0,r.jsx)("div",{className:"text-gray-600 font-medium",children:"Loading discussions..."})]})}):(0,r.jsxs)("div",{className:"flex flex-col bg-white rounded-lg shadow-sm",children:[(0,r.jsxs)("div",{className:"bg-gradient-to-r from-blue-50 to-indigo-50 border border-gray-200 rounded-lg p-6 mb-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)("div",{className:"bg-gradient-to-r from-blue-500 to-indigo-600 p-3 rounded-xl",children:(0,r.jsx)(l.g,{icon:o.q9p,className:"text-white text-xl"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-800",children:"Discussion Forum"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Connect with other learners, ask questions, and share insights"})]})]}),(0,r.jsxs)("button",{onClick:()=>g(!0),className:"bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white px-6 py-3 rounded-xl flex items-center gap-2 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5",children:[(0,r.jsx)(l.g,{icon:o.QLR}),"Ask a Question"]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-6 pt-4 border-t border-gray-100",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 text-gray-600",children:[(0,r.jsx)(l.g,{icon:o.q9p,className:"text-blue-500"}),(0,r.jsx)("span",{className:"font-medium",children:t.length}),(0,r.jsx)("span",{children:"Discussions"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 text-gray-600",children:[(0,r.jsx)(l.g,{icon:o.gdJ,className:"text-green-500"}),(0,r.jsx)("span",{className:"font-medium",children:new Set(t.map(e=>e.username)).size}),(0,r.jsx)("span",{children:"Active Members"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 text-gray-600",children:[(0,r.jsx)(l.g,{icon:o.kNw,className:"text-orange-500"}),(0,r.jsx)("span",{className:"font-medium",children:t.reduce((e,s)=>e+s.reply_count,0)}),(0,r.jsx)("span",{children:"Total Replies"})]})]})]}),h&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,r.jsxs)("div",{className:"bg-white rounded-2xl p-8 max-w-2xl w-full max-h-[90vh] overflow-y-auto shadow-2xl",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"bg-gradient-to-r from-blue-500 to-indigo-600 p-2 rounded-lg",children:(0,r.jsx)(l.g,{icon:o.DN2,className:"text-white"})}),(0,r.jsx)("h3",{className:"text-2xl font-bold text-gray-800",children:"Ask a Question"})]}),(0,r.jsx)("button",{onClick:()=>g(!1),className:"text-gray-400 hover:text-gray-600 text-xl",children:(0,r.jsx)(l.g,{icon:o.GRI})})]}),(0,r.jsxs)("form",{onSubmit:q,className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-semibold text-gray-700 mb-3",children:"Question Title *"}),(0,r.jsx)("input",{type:"text",value:N,onChange:e=>w(e.target.value),placeholder:"What's your question about?",className:"w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-blue-500 focus:ring-4 focus:ring-blue-100 transition-all duration-200",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-semibold text-gray-700 mb-3",children:"Question Details *"}),(0,r.jsx)("textarea",{value:k,onChange:e=>C(e.target.value),placeholder:"Provide more context about your question. The more details you share, the better answers you'll get!",rows:6,className:"w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-blue-500 focus:ring-4 focus:ring-blue-100 transition-all duration-200 resize-none",required:!0})]}),(0,r.jsxs)("div",{className:"flex gap-3 pt-4",children:[(0,r.jsx)("button",{type:"submit",disabled:y,className:"flex-1 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 disabled:from-gray-400 disabled:to-gray-500 text-white px-6 py-3 rounded-xl flex items-center justify-center gap-2 font-semibold transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:transform-none",children:y?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),"Posting..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(l.g,{icon:o.isI}),"Post Question"]})}),(0,r.jsx)("button",{type:"button",onClick:()=>g(!1),className:"px-6 py-3 border-2 border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 font-semibold transition-all duration-200",children:"Cancel"})]})]})]})}),(0,r.jsxs)("div",{className:"flex-1 flex gap-6",children:[(0,r.jsxs)("div",{className:"w-1/2 bg-white rounded-2xl shadow-xl overflow-hidden border border-gray-200",children:[(0,r.jsxs)("div",{className:"bg-gradient-to-r from-blue-500 to-indigo-600 px-6 py-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h3",{className:"font-bold text-white text-lg",children:"All Discussions"}),(0,r.jsxs)("div",{className:"text-white text-sm",children:[I.length," of ",t.length]})]}),(0,r.jsxs)("div",{className:"mt-4 relative",children:[(0,r.jsx)(l.g,{icon:o.MjD,className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-blue-200"}),(0,r.jsx)("input",{type:"text",value:j,onChange:e=>b(e.target.value),placeholder:"Search discussions...",className:"w-full pl-10 pr-4 py-2 bg-white bg-opacity-20 border border-blue-300 rounded-lg text-white placeholder-blue-200 focus:outline-none focus:bg-opacity-30 focus:border-blue-100"})]})]}),(0,r.jsx)("div",{className:"overflow-y-auto max-h-[600px]",children:0===I.length?(0,r.jsx)("div",{className:"p-8 text-center text-gray-500",children:j?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(l.g,{icon:o.MjD,className:"text-4xl mb-3 text-gray-300"}),(0,r.jsxs)("p",{children:['No discussions found matching "',j,'"']})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(l.g,{icon:o.q9p,className:"text-4xl mb-3 text-gray-300"}),(0,r.jsx)("p",{children:"No discussions yet. Be the first to ask a question!"})]})}):I.map(e=>(0,r.jsx)("div",{onClick:()=>D(e),className:`p-5 border-b border-gray-100 cursor-pointer transition-all duration-200 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 ${n?.id===e.id?"bg-gradient-to-r from-blue-50 to-indigo-50 border-l-4 border-l-blue-500 shadow-inner":""}`,children:(0,r.jsxs)("div",{className:"flex items-start gap-4",children:[(0,r.jsx)("div",{className:"bg-gradient-to-r from-blue-500 to-indigo-600 text-white w-10 h-10 rounded-full flex items-center justify-center font-semibold text-sm flex-shrink-0",children:L(e.first_name,e.last_name)}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("h4",{className:"font-semibold text-gray-800 mb-2 line-clamp-2",children:e.title}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-3 line-clamp-2",children:e.content}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 text-xs text-gray-500",children:[(0,r.jsxs)("span",{className:"font-medium",children:[e.first_name," ",e.last_name]}),(0,r.jsx)("span",{children:"•"}),(0,r.jsx)("span",{children:E(e.created_at)})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsxs)("div",{className:"flex items-center gap-1 text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full",children:[(0,r.jsx)(l.g,{icon:o.Eze}),(0,r.jsx)("span",{children:e.reply_count})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1 text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full",children:[(0,r.jsx)(l.g,{icon:o.pS3}),(0,r.jsx)("span",{children:Math.floor(50*Math.random())+10})]})]})]})]})]})},e.id))})]}),(0,r.jsx)("div",{className:"w-1/2 bg-white rounded-2xl shadow-xl overflow-hidden border border-gray-200",children:n?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"bg-gradient-to-r from-indigo-500 to-purple-600 px-6 py-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h3",{className:"font-bold text-white text-lg",children:"Discussion Details"}),(0,r.jsxs)("div",{className:"flex items-center gap-2 text-indigo-200 text-sm",children:[(0,r.jsx)(l.g,{icon:o.Eze}),(0,r.jsxs)("span",{children:[d.length," replies"]})]})]})}),(0,r.jsxs)("div",{className:"flex flex-col h-[600px]",children:[(0,r.jsxs)("div",{className:"flex-1 overflow-y-auto p-6",children:[(0,r.jsxs)("div",{className:"mb-8 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl border-l-4 border-blue-500",children:[(0,r.jsxs)("div",{className:"flex items-start gap-4 mb-4",children:[(0,r.jsx)("div",{className:"bg-gradient-to-r from-blue-500 to-indigo-600 text-white w-12 h-12 rounded-full flex items-center justify-center font-bold text-lg flex-shrink-0",children:L(n.first_name,n.last_name)}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h4",{className:"font-bold text-gray-800 text-lg mb-2",children:n.title}),(0,r.jsxs)("div",{className:"flex items-center gap-3 text-sm text-gray-600 mb-3",children:[(0,r.jsxs)("span",{className:"font-semibold",children:[n.first_name," ",n.last_name]}),(0,r.jsx)("span",{className:"bg-blue-100 text-blue-700 px-2 py-1 rounded-full text-xs",children:"Original Poster"}),(0,r.jsx)("span",{children:"•"}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(l.g,{icon:o.BEE}),(0,r.jsx)("span",{children:E(n.created_at)})]})]})]})]}),(0,r.jsx)("p",{className:"text-gray-700 leading-relaxed",children:n.content})]}),d.length>0&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("h5",{className:"font-semibold text-gray-800 flex items-center gap-2",children:[(0,r.jsx)(l.g,{icon:o.q9p,className:"text-indigo-500"}),"Replies (",d.length,")"]}),d.map((e,s)=>(0,r.jsx)("div",{className:"p-4 bg-gray-50 rounded-xl border border-gray-200 hover:shadow-md transition-shadow duration-200",children:(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[(0,r.jsx)("div",{className:"bg-gradient-to-r from-gray-500 to-gray-600 text-white w-10 h-10 rounded-full flex items-center justify-center font-semibold text-sm flex-shrink-0",children:L(e.first_name,e.last_name)}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,r.jsxs)("span",{className:"font-semibold text-gray-800",children:[e.first_name," ",e.last_name]}),(0,r.jsx)("span",{className:"text-gray-400",children:"•"}),(0,r.jsx)("span",{className:"text-sm text-gray-500",children:E(e.created_at)}),(0,r.jsxs)("span",{className:"bg-green-100 text-green-700 px-2 py-1 rounded-full text-xs",children:["#",s+1]})]}),(0,r.jsx)("p",{className:"text-gray-700 leading-relaxed",children:e.content})]})]})},e.id))]})]}),(0,r.jsx)("div",{className:"border-t border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100 p-6",children:f?(0,r.jsxs)("form",{onSubmit:A,className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,r.jsx)("div",{className:"bg-gradient-to-r from-green-500 to-emerald-600 text-white w-8 h-8 rounded-full flex items-center justify-center font-semibold text-sm",children:(0,r.jsx)(l.g,{icon:o.Eze})}),(0,r.jsx)("span",{className:"font-semibold text-gray-700",children:"Add your reply"})]}),(0,r.jsx)("textarea",{value:_,onChange:e=>S(e.target.value),placeholder:"Share your thoughts, provide an answer, or ask for clarification...",rows:4,className:"w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-indigo-500 focus:ring-4 focus:ring-indigo-100 transition-all duration-200 resize-none",required:!0}),(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsx)("button",{type:"submit",disabled:y,className:"flex-1 bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 disabled:from-gray-400 disabled:to-gray-500 text-white px-4 py-3 rounded-xl flex items-center justify-center gap-2 font-semibold transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:transform-none",children:y?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),"Posting..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(l.g,{icon:o.isI}),"Post Reply"]})}),(0,r.jsx)("button",{type:"button",onClick:()=>p(!1),className:"px-6 py-3 border-2 border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 font-semibold transition-all duration-200",children:"Cancel"})]})]}):(0,r.jsxs)("button",{onClick:()=>p(!0),className:"w-full bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white py-4 rounded-xl flex items-center justify-center gap-3 font-semibold transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5",children:[(0,r.jsx)(l.g,{icon:o.Eze,className:"text-lg"}),"Join the Discussion"]})})]})]}):(0,r.jsx)("div",{className:"flex items-center justify-center h-[600px] text-gray-500",children:(0,r.jsxs)("div",{className:"text-center p-8",children:[(0,r.jsx)("div",{className:"bg-gradient-to-r from-gray-200 to-gray-300 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(l.g,{icon:o.q9p,className:"text-3xl text-gray-400"})}),(0,r.jsx)("h4",{className:"text-lg font-semibold text-gray-600 mb-2",children:"Select a Discussion"}),(0,r.jsx)("p",{className:"text-gray-500",children:"Choose a discussion from the left to view details and join the conversation"})]})})})]})]}):(0,r.jsx)("div",{className:"flex justify-center items-center py-12",children:(0,r.jsx)("div",{className:"flex flex-col items-center",children:(0,r.jsx)("div",{className:"text-gray-600 font-medium",children:"Please log in to access discussions"})})})}function d(){let e=(0,i.useParams)(),s=(0,i.useRouter)(),t=e.id,[l,o]=(0,a.useState)(null),[d,x]=(0,a.useState)(null),[m,u]=(0,a.useState)(!0),h=d?.id,g=()=>{s.push(`/courses/${t}`)};return m?(0,r.jsxs)("div",{className:"flex flex-col min-h-screen bg-gray-50",children:[(0,r.jsx)(n.default,{}),(0,r.jsx)("main",{className:"flex-grow flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading discussion forum..."})]})})]}):(0,r.jsxs)("div",{className:"flex flex-col min-h-screen bg-gray-50",children:[(0,r.jsx)(n.default,{}),(0,r.jsx)("div",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between py-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("button",{onClick:g,className:"flex items-center text-gray-600 hover:text-gray-900 transition-colors duration-200",children:[(0,r.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})}),"Back to Course"]}),l&&(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"h-6 w-px bg-gray-300"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-lg font-semibold text-gray-900",children:l.title}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Discussion Forum"})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:l?.domain}),(0,r.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:l?.level})]})]})})}),(0,r.jsx)("main",{className:"flex-grow",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:h?(0,r.jsx)(c,{courseId:t,userId:h}):(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-8 text-center",children:(0,r.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,r.jsx)("svg",{className:"w-16 h-16 text-gray-400 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Login Required"}),(0,r.jsx)("p",{className:"text-gray-500 mb-6",children:"Please log in to access the discussion forum and connect with other learners."}),(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsx)("button",{onClick:()=>s.push("/signin"),className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:"Go to Login"}),(0,r.jsx)("button",{onClick:g,className:"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:"Back to Course"})]})]})})})})]})}},7910:e=>{"use strict";e.exports=require("stream")},8335:()=>{},8354:e=>{"use strict";e.exports=require("util")},9021:e=>{"use strict";e.exports=require("fs")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9190:(e,s,t)=>{"use strict";t.d(s,{default:()=>c});var r=t(687),a=t(3210),i=t(5885),n=t(7e3),l=t(7346),o=t(7351);let c=()=>{let[e,s]=(0,a.useState)(!1),[t,c]=(0,a.useState)(null),d=(0,a.useRef)(null),x=e=>{try{let s=e.split(".")[1].replace(/-/g,"+").replace(/_/g,"/"),t=decodeURIComponent(atob(s).split("").map(function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)}).join(""));return JSON.parse(t)}catch(e){return console.error("Error decoding JWT:",e),null}};(0,a.useEffect)(()=>{let e=localStorage.getItem("token");if(e){let s=x(e);s&&c({id:s.id,email:s.email,username:s.username,role:s.role,first_name:s.first_name||s.username,last_name:s.last_name||""})}},[]);let m=(e,s)=>(e?e.charAt(0).toUpperCase():"")+(s?s.charAt(0).toUpperCase():"")||"U";return(0,a.useEffect)(()=>{let e=e=>{d.current&&!d.current.contains(e.target)&&s(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]),(0,r.jsxs)("nav",{className:"bg-white p-4 shadow-md",children:[(0,r.jsxs)("div",{className:"container mx-auto flex justify-between items-center",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"bg-orange-500 rounded-xl p-2 flex items-center justify-center rounded-lg",children:(0,r.jsx)(o.A,{className:"w-6 h-6 text-white"})}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"text-orange-500 text-lg font-bold",children:"EX"}),(0,r.jsx)("span",{className:"text-[#005071] text-lg font-bold",children:"Learn"})]})]}),(0,r.jsx)("div",{className:"flex-grow flex justify-center mx-4",children:(0,r.jsx)("div",{className:"relative w-full max-w-[280px]",children:(0,r.jsx)("input",{type:"text",placeholder:"Search courses, topics, or instructors...",className:"w-full pl-3 pr-3 py-1 rounded-full border border-transparent focus:outline-none focus:ring-1 focus:ring-orange-100 text-xs placeholder-gray-400"})})}),(0,r.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,r.jsx)(l.A,{}),(0,r.jsx)("div",{className:"relative",ref:d,children:t?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 cursor-pointer hover:bg-gray-50 rounded-lg px-2 py-1 transition-colors duration-200",onClick:()=>{s(!e)},children:[(0,r.jsx)("div",{className:"bg-orange-500 text-white rounded-full h-8 w-8 flex items-center justify-center font-medium",children:m(t.first_name,t.last_name)}),(0,r.jsx)("span",{className:"text-gray-700 text-sm font-medium",children:t.first_name}),(0,r.jsx)(i.g,{icon:n.Jt$,className:`text-gray-500 text-xs transition-transform duration-200 ${e?"rotate-180":""}`})]}),e&&(0,r.jsxs)("div",{className:"absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50",children:[(0,r.jsx)("div",{className:"px-4 py-3 border-b border-gray-100",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"bg-orange-500 text-white rounded-full h-10 w-10 flex items-center justify-center font-medium",children:m(t.first_name,t.last_name)}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{className:"text-sm font-medium text-gray-900",children:[t.first_name," ",t.last_name]}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:t.email})]})]})}),(0,r.jsxs)("div",{className:"py-1",children:[(0,r.jsxs)("button",{className:"w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200",onClick:()=>{s(!1)},children:[(0,r.jsx)(i.g,{icon:n.X46,className:"mr-3 text-gray-400"}),"Profile"]}),(0,r.jsxs)("button",{className:"w-full flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors duration-200",onClick:()=>{localStorage.removeItem("token"),sessionStorage.removeItem("token"),localStorage.removeItem("authToken"),sessionStorage.removeItem("authToken"),c(null),window.location.href="/signin"},children:[(0,r.jsx)(i.g,{icon:n.GxD,className:"mr-3 text-red-500"}),"Log out"]})]})]})]}):(0,r.jsxs)("a",{href:"/signin",className:"flex items-center space-x-2 bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors duration-200",children:[(0,r.jsx)(i.g,{icon:n.X46,className:"text-sm"}),(0,r.jsx)("span",{className:"text-sm font-medium",children:"Sign In"})]})})]})]}),(0,r.jsx)("div",{className:"container mx-auto mt-4",children:(0,r.jsxs)("ul",{className:"flex space-x-8",children:[(0,r.jsx)("li",{children:(0,r.jsxs)("a",{href:"/",className:"flex items-center space-x-2 text-orange-500 border-b-2 border-orange-500 pb-2",children:[(0,r.jsx)(i.g,{icon:n.v02,className:"text-sm"}),(0,r.jsx)("span",{children:"Home"})]})}),(0,r.jsx)("li",{children:(0,r.jsxs)("a",{href:"/my-dashboard",className:"flex items-center space-x-2 text-gray-700 hover:text-orange-500",children:[(0,r.jsx)(i.g,{icon:n.xiI,className:"text-sm"}),(0,r.jsx)("span",{children:"My Dashboard"})]})}),(0,r.jsx)("li",{children:(0,r.jsxs)("a",{href:"/my-learning",className:"flex items-center space-x-2 text-gray-700 hover:text-orange-500",children:[(0,r.jsx)(i.g,{icon:n.ReK,className:"text-sm"}),(0,r.jsx)("span",{children:"My Learning"})]})}),(0,r.jsx)("li",{children:(0,r.jsxs)("a",{href:"/my-certificates",className:"flex items-center space-x-2 text-gray-700 hover:text-orange-500",children:[(0,r.jsx)(i.g,{icon:n.fmL,className:"text-sm"}),(0,r.jsx)("span",{children:"Certificates"})]})})]})})]})}},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9551:e=>{"use strict";e.exports=require("url")},9646:e=>{"use strict";e.exports=require("child_process")},9690:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\AI-Trainer3\\frontend\\src\\app\\courses\\[id]\\discussion\\page.tsx","default")}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,773,658,578],()=>t(5817));module.exports=r})();