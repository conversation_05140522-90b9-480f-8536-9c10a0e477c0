"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[25],{437:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("check-check",[["path",{d:"M18 6 7 17l-5-5",key:"116fxf"}],["path",{d:"m22 10-7.5 7.5L13 16",key:"ke71qq"}]])},8905:(e,t,n)=>{n.d(t,{C:()=>l});var r=n(2115),o=n(6101),i=n(2712),l=e=>{let{present:t,children:n}=e,l=function(e){var t,n;let[o,l]=r.useState(),u=r.useRef(null),s=r.useRef(e),c=r.useRef("none"),[d,p]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=a(u.current);c.current="mounted"===d?e:"none"},[d]),(0,i.N)(()=>{let t=u.current,n=s.current;if(n!==e){let r=c.current,o=a(t);e?p("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?p("UNMOUNT"):n&&r!==o?p("ANIMATION_OUT"):p("UNMOUNT"),s.current=e}},[e,p]),(0,i.N)(()=>{if(o){var e;let t,n=null!=(e=o.ownerDocument.defaultView)?e:window,r=e=>{let r=a(u.current).includes(e.animationName);if(e.target===o&&r&&(p("ANIMATION_END"),!s.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},i=e=>{e.target===o&&(c.current=a(u.current))};return o.addEventListener("animationstart",i),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",i),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}p("ANIMATION_END")},[o,p]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{u.current=e?getComputedStyle(e):null,l(e)},[])}}(t),u="function"==typeof n?n({present:l.isPresent}):r.Children.only(n),s=(0,o.s)(l.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(u));return"function"==typeof n||l.isPresent?r.cloneElement(u,{ref:s}):null};function a(e){return(null==e?void 0:e.animationName)||"none"}l.displayName="Presence"},9613:(e,t,n)=>{n.d(t,{Kq:()=>X,UC:()=>G,ZL:()=>z,bL:()=>Y,i3:()=>K,l9:()=>Z});var r=n(2115),o=n(5185),i=n(6101),l=n(6081),a=n(9178),u=n(1285),s=n(8795),c=n(4378),d=n(8905),p=n(3655),f=n(9708),m=n(5845),v=n(2564),h=n(5155),[g,x]=(0,l.A)("Tooltip",[s.Bk]),y=(0,s.Bk)(),w="TooltipProvider",b="tooltip.open",[T,C]=g(w),E=e=>{let{__scopeTooltip:t,delayDuration:n=700,skipDelayDuration:o=300,disableHoverableContent:i=!1,children:l}=e,a=r.useRef(!0),u=r.useRef(!1),s=r.useRef(0);return r.useEffect(()=>{let e=s.current;return()=>window.clearTimeout(e)},[]),(0,h.jsx)(T,{scope:t,isOpenDelayedRef:a,delayDuration:n,onOpen:r.useCallback(()=>{window.clearTimeout(s.current),a.current=!1},[]),onClose:r.useCallback(()=>{window.clearTimeout(s.current),s.current=window.setTimeout(()=>a.current=!0,o)},[o]),isPointerInTransitRef:u,onPointerInTransitChange:r.useCallback(e=>{u.current=e},[]),disableHoverableContent:i,children:l})};E.displayName=w;var N="Tooltip",[k,L]=g(N),M=e=>{let{__scopeTooltip:t,children:n,open:o,defaultOpen:i,onOpenChange:l,disableHoverableContent:a,delayDuration:c}=e,d=C(N,e.__scopeTooltip),p=y(t),[f,v]=r.useState(null),g=(0,u.B)(),x=r.useRef(0),w=null!=a?a:d.disableHoverableContent,T=null!=c?c:d.delayDuration,E=r.useRef(!1),[L,M]=(0,m.i)({prop:o,defaultProp:null!=i&&i,onChange:e=>{e?(d.onOpen(),document.dispatchEvent(new CustomEvent(b))):d.onClose(),null==l||l(e)},caller:N}),R=r.useMemo(()=>L?E.current?"delayed-open":"instant-open":"closed",[L]),O=r.useCallback(()=>{window.clearTimeout(x.current),x.current=0,E.current=!1,M(!0)},[M]),j=r.useCallback(()=>{window.clearTimeout(x.current),x.current=0,M(!1)},[M]),_=r.useCallback(()=>{window.clearTimeout(x.current),x.current=window.setTimeout(()=>{E.current=!0,M(!0),x.current=0},T)},[T,M]);return r.useEffect(()=>()=>{x.current&&(window.clearTimeout(x.current),x.current=0)},[]),(0,h.jsx)(s.bL,{...p,children:(0,h.jsx)(k,{scope:t,contentId:g,open:L,stateAttribute:R,trigger:f,onTriggerChange:v,onTriggerEnter:r.useCallback(()=>{d.isOpenDelayedRef.current?_():O()},[d.isOpenDelayedRef,_,O]),onTriggerLeave:r.useCallback(()=>{w?j():(window.clearTimeout(x.current),x.current=0)},[j,w]),onOpen:O,onClose:j,disableHoverableContent:w,children:n})})};M.displayName=N;var R="TooltipTrigger",O=r.forwardRef((e,t)=>{let{__scopeTooltip:n,...l}=e,a=L(R,n),u=C(R,n),c=y(n),d=r.useRef(null),f=(0,i.s)(t,d,a.onTriggerChange),m=r.useRef(!1),v=r.useRef(!1),g=r.useCallback(()=>m.current=!1,[]);return r.useEffect(()=>()=>document.removeEventListener("pointerup",g),[g]),(0,h.jsx)(s.Mz,{asChild:!0,...c,children:(0,h.jsx)(p.sG.button,{"aria-describedby":a.open?a.contentId:void 0,"data-state":a.stateAttribute,...l,ref:f,onPointerMove:(0,o.m)(e.onPointerMove,e=>{"touch"!==e.pointerType&&(v.current||u.isPointerInTransitRef.current||(a.onTriggerEnter(),v.current=!0))}),onPointerLeave:(0,o.m)(e.onPointerLeave,()=>{a.onTriggerLeave(),v.current=!1}),onPointerDown:(0,o.m)(e.onPointerDown,()=>{a.open&&a.onClose(),m.current=!0,document.addEventListener("pointerup",g,{once:!0})}),onFocus:(0,o.m)(e.onFocus,()=>{m.current||a.onOpen()}),onBlur:(0,o.m)(e.onBlur,a.onClose),onClick:(0,o.m)(e.onClick,a.onClose)})})});O.displayName=R;var j="TooltipPortal",[_,P]=g(j,{forceMount:void 0}),I=e=>{let{__scopeTooltip:t,forceMount:n,children:r,container:o}=e,i=L(j,t);return(0,h.jsx)(_,{scope:t,forceMount:n,children:(0,h.jsx)(d.C,{present:n||i.open,children:(0,h.jsx)(c.Z,{asChild:!0,container:o,children:r})})})};I.displayName=j;var A="TooltipContent",D=r.forwardRef((e,t)=>{let n=P(A,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...i}=e,l=L(A,e.__scopeTooltip);return(0,h.jsx)(d.C,{present:r||l.open,children:l.disableHoverableContent?(0,h.jsx)(W,{side:o,...i,ref:t}):(0,h.jsx)(U,{side:o,...i,ref:t})})}),U=r.forwardRef((e,t)=>{let n=L(A,e.__scopeTooltip),o=C(A,e.__scopeTooltip),l=r.useRef(null),a=(0,i.s)(t,l),[u,s]=r.useState(null),{trigger:c,onClose:d}=n,p=l.current,{onPointerInTransitChange:f}=o,m=r.useCallback(()=>{s(null),f(!1)},[f]),v=r.useCallback((e,t)=>{let n=e.currentTarget,r={x:e.clientX,y:e.clientY},o=function(e,t){let n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(n,r,o,i)){case i:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw Error("unreachable")}}(r,n.getBoundingClientRect());s(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:1*!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let n=0;n<e.length;n++){let r=e[n];for(;t.length>=2;){let e=t[t.length-1],n=t[t.length-2];if((e.x-n.x)*(r.y-n.y)>=(e.y-n.y)*(r.x-n.x))t.pop();else break}t.push(r)}t.pop();let n=[];for(let t=e.length-1;t>=0;t--){let r=e[t];for(;n.length>=2;){let e=n[n.length-1],t=n[n.length-2];if((e.x-t.x)*(r.y-t.y)>=(e.y-t.y)*(r.x-t.x))n.pop();else break}n.push(r)}return(n.pop(),1===t.length&&1===n.length&&t[0].x===n[0].x&&t[0].y===n[0].y)?t:t.concat(n)}(t)}([...function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n})}return r}(r,o),...function(e){let{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}(t.getBoundingClientRect())])),f(!0)},[f]);return r.useEffect(()=>()=>m(),[m]),r.useEffect(()=>{if(c&&p){let e=e=>v(e,p),t=e=>v(e,c);return c.addEventListener("pointerleave",e),p.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),p.removeEventListener("pointerleave",t)}}},[c,p,v,m]),r.useEffect(()=>{if(u){let e=e=>{let t=e.target,n={x:e.clientX,y:e.clientY},r=(null==c?void 0:c.contains(t))||(null==p?void 0:p.contains(t)),o=!function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let l=t[e],a=t[i],u=l.x,s=l.y,c=a.x,d=a.y;s>r!=d>r&&n<(c-u)*(r-s)/(d-s)+u&&(o=!o)}return o}(n,u);r?m():o&&(m(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,p,u,d,m]),(0,h.jsx)(W,{...e,ref:a})}),[B,F]=g(N,{isInside:!1}),S=(0,f.Dc)("TooltipContent"),W=r.forwardRef((e,t)=>{let{__scopeTooltip:n,children:o,"aria-label":i,onEscapeKeyDown:l,onPointerDownOutside:u,...c}=e,d=L(A,n),p=y(n),{onClose:f}=d;return r.useEffect(()=>(document.addEventListener(b,f),()=>document.removeEventListener(b,f)),[f]),r.useEffect(()=>{if(d.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(d.trigger))&&f()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[d.trigger,f]),(0,h.jsx)(a.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:l,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:f,children:(0,h.jsxs)(s.UC,{"data-state":d.stateAttribute,...p,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,h.jsx)(S,{children:o}),(0,h.jsx)(B,{scope:n,isInside:!0,children:(0,h.jsx)(v.bL,{id:d.contentId,role:"tooltip",children:i||o})})]})})});D.displayName=A;var q="TooltipArrow",H=r.forwardRef((e,t)=>{let{__scopeTooltip:n,...r}=e,o=y(n);return F(q,n).isInside?null:(0,h.jsx)(s.i3,{...o,...r,ref:t})});H.displayName=q;var X=E,Y=M,Z=O,z=I,G=D,K=H}}]);