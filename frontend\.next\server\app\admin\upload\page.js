(()=>{var e={};e.id=798,e.ids=[798],e.modules={13:(e,t,r)=>{"use strict";r.d(t,{J:()=>l});var s=r(687);r(3210);var a=r(8148),i=r(4780);function l({className:e,...t}){return(0,s.jsx)(a.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},38:(e,t,r)=>{Promise.resolve().then(r.bind(r,6048))},440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(1658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1820:e=>{"use strict";e.exports=require("os")},1860:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(2688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},3997:e=>{"use strict";e.exports=require("tty")},4075:e=>{"use strict";e.exports=require("zlib")},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},4737:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(5239),a=r(8088),i=r(8170),l=r.n(i),n=r(893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);r.d(t,o);let d={children:["",{children:["admin",{children:["upload",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,8230)),"C:\\Projects\\AI-Trainer3\\frontend\\src\\app\\admin\\upload\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,8429)),"C:\\Projects\\AI-Trainer3\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Projects\\AI-Trainer3\\frontend\\src\\app\\admin\\upload\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/upload/page",pathname:"/admin/upload",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},5079:(e,t,r)=>{"use strict";r.d(t,{bq:()=>u,eb:()=>m,gC:()=>p,l6:()=>d,yv:()=>c});var s=r(687);r(3210);var a=r(19),i=r(5891),l=r(3964),n=r(3589),o=r(4780);function d({...e}){return(0,s.jsx)(a.bL,{"data-slot":"select",...e})}function c({...e}){return(0,s.jsx)(a.WT,{"data-slot":"select-value",...e})}function u({className:e,size:t="default",children:r,...l}){return(0,s.jsxs)(a.l9,{"data-slot":"select-trigger","data-size":t,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...l,children:[r,(0,s.jsx)(a.In,{asChild:!0,children:(0,s.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function p({className:e,children:t,position:r="popper",...i}){return(0,s.jsx)(a.ZL,{children:(0,s.jsxs)(a.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...i,children:[(0,s.jsx)(x,{}),(0,s.jsx)(a.LM,{className:(0,o.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,s.jsx)(h,{})]})})}function m({className:e,children:t,...r}){return(0,s.jsxs)(a.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...r,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(a.VF,{children:(0,s.jsx)(l.A,{className:"size-4"})})}),(0,s.jsx)(a.p4,{children:t})]})}function x({className:e,...t}){return(0,s.jsx)(a.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(n.A,{className:"size-4"})})}function h({className:e,...t}){return(0,s.jsx)(a.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(i.A,{className:"size-4"})})}},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},6048:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var s=r(687),a=r(3210);let i=(0,r(2688).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]);var l=r(22),n=r(1860),o=r(9523),d=r(4780);function c({className:e,type:t,...r}){return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,d.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}function u({className:e,...t}){return(0,s.jsx)("textarea",{"data-slot":"textarea",className:(0,d.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...t})}var p=r(13),m=r(5079);let x=({files:e=[],onFilesChange:t,onFileAdd:r,onFileRemove:d,onFileProgress:c,maxFiles:u=10,maxFileSize:p=0xa00000,acceptedFileTypes:m=[".pdf",".doc",".docx",".txt",".jpg",".jpeg",".png"],multiple:x=!0,title:h="Upload Files",subtitle:g="Upload documents you want to share with your team",dragText:f="Drag and drop here",browseButtonText:v="Browse files",showProgress:b=!0,showFileList:j=!0,colors:y=["blue","green","yellow","red","purple"],className:w="",onUploadStart:N,onUploadComplete:C,onUploadError:P,uploadFunction:_})=>{let[k,q]=(0,a.useState)([]),[z,F]=(0,a.useState)(!1),A=(0,a.useRef)(null),M=t?e:k,$=e=>{if(e.size>p)return`File size must be less than ${Math.round(p/1024/1024)}MB`;let t="."+e.name.split(".").pop()?.toLowerCase();return m.includes(t)?M.length>=u?`Maximum ${u} files allowed`:null:`File type not supported. Accepted types: ${m.join(", ")}`},D=()=>`file_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,E=()=>y[Math.floor(Math.random()*y.length)],S=(0,a.useCallback)(e=>{e.preventDefault(),e.stopPropagation(),"dragenter"===e.type||"dragover"===e.type?F(!0):"dragleave"===e.type&&F(!1)},[]),T=(0,a.useCallback)(e=>{e.preventDefault(),e.stopPropagation(),F(!1),e.dataTransfer.files&&e.dataTransfer.files[0]&&B(e.dataTransfer.files)},[M.length,u]),U=(0,a.useCallback)(e=>{e.preventDefault(),e.target.files&&e.target.files[0]&&B(e.target.files)},[M.length,u]),B=(0,a.useCallback)(e=>{let s=[],a=[];if(Array.from(e).forEach(e=>{let t=$(e);t?a.push(`${e.name}: ${t}`):s.push(e)}),a.length>0&&(console.error("File validation errors:",a),alert(a.join("\n"))),0===s.length)return;if(r)return void r(s);let i=s.map(e=>({id:D(),name:e.name,progress:0,status:"uploading",color:E(),file:e,size:e.size,type:e.type}));(t||q)(e=>[...e,...i]),i.forEach(e=>{N&&N(e.file),_?_(e.file,t=>{G(e.id,t)}).then(()=>{J(e.id,"completed"),C&&C(e)}).catch(t=>{J(e.id,"error"),P&&P(e,t.message)}):I(e.id)})},[M,u,p,m,r,N,_,t,q]),I=e=>{let t=0,r=setInterval(()=>{(t+=30*Math.random())>=100&&(t=100,clearInterval(r),J(e,"completed")),G(e,Math.floor(t))},500)},G=(e,r)=>{let s=t||q;c&&c(e,r),s(t=>t.map(t=>t.id===e?{...t,progress:r}:t))},J=(e,r)=>{(t||q)(t=>t.map(t=>t.id===e?{...t,status:r,progress:"completed"===r?100:t.progress}:t))},R=(0,a.useCallback)(e=>{let r=t||q;d?d(e):r(t=>t.filter(t=>t.id!==e))},[d,t,q]),L=e=>({blue:"bg-blue-500",green:"bg-green-500",yellow:"bg-yellow-500",red:"bg-red-500",purple:"bg-purple-500"})[e],O=e=>({blue:"text-blue-500",green:"text-green-500",yellow:"text-yellow-500",red:"text-red-500",purple:"text-purple-500"})[e],V=e=>{switch(e){case"completed":return"text-green-600";case"error":return"text-red-600";default:return"text-gray-500"}},H=e=>{switch(e){case"completed":return"Completed";case"error":return"Error";default:return"Uploading..."}},K=e=>{if(0===e)return"0 Bytes";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB"][t]};return(0,s.jsx)("div",{className:`max-w-7xl mx-auto p-6 ${w}`,children:(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-lg p-6",children:[(0,s.jsx)("div",{className:"mb-6"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 items-start",children:[(0,s.jsx)("div",{className:"space-y-4",children:(0,s.jsxs)("div",{className:`border-2 border-dashed rounded-xl p-8 text-center transition-all duration-300 ${z?"border-[#005071] bg-blue-50":"border-gray-300 hover:border-[#005071] hover:bg-gray-50"}`,onDragEnter:S,onDragLeave:S,onDragOver:S,onDrop:T,children:[(0,s.jsx)("div",{className:"mb-4",children:(0,s.jsx)(i,{className:"mx-auto h-12 w-12 text-gray-400"})}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-700 mb-1",children:f}),(0,s.jsx)("p",{className:"text-gray-500 text-sm mb-4",children:"-OR-"}),(0,s.jsx)(o.$,{onClick:()=>{A.current?.click()},className:"bg-[#005071] hover:bg-[#005071]/[90%] text-white px-6 py-2.5 rounded-lg font-medium transition-colors duration-200",children:v}),(0,s.jsx)("input",{ref:A,type:"file",multiple:x,onChange:U,className:"hidden",accept:m.join(",")})]})}),j&&(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsxs)("h3",{className:"text-lg font-semibold text-gray-900",children:["Uploaded Files (",M.length,")"]})}),(0,s.jsxs)("div",{className:"max-h-96 overflow-y-auto space-y-3 pr-2",children:[M.map(e=>(0,s.jsxs)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-3 hover:shadow-sm transition-shadow",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 flex-1 min-w-0",children:[(0,s.jsx)(l.A,{className:`h-4 w-4 ${O(e.color)} flex-shrink-0`}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("p",{className:"font-medium text-gray-700 truncate text-sm",children:e.name}),e.size&&(0,s.jsx)("p",{className:"text-xs text-gray-500",children:K(e.size)})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 flex-shrink-0",children:[b&&(0,s.jsxs)("span",{className:`text-xs ${O(e.color)}`,children:[e.progress,"%"]}),(0,s.jsx)("span",{className:`text-xs ${V(e.status)}`,children:H(e.status)}),(0,s.jsx)("button",{onClick:()=>R(e.id),className:"text-gray-400 hover:text-red-500 transition-colors p-1","aria-label":`Remove ${e.name}`,children:(0,s.jsx)(n.A,{className:"h-3 w-3"})})]})]}),b&&(0,s.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-1.5",children:(0,s.jsx)("div",{className:`h-1.5 rounded-full transition-all duration-300 ${"error"===e.status?"bg-red-500":L(e.color)}`,style:{width:`${e.progress}%`}})})]},e.id)),0===M.length&&(0,s.jsxs)("div",{className:"text-center py-8 text-gray-500 bg-gray-50 rounded-lg border-2 border-dashed border-gray-200",children:[(0,s.jsx)(l.A,{className:"mx-auto h-8 w-8 text-gray-300 mb-2"}),(0,s.jsx)("p",{className:"text-sm",children:"No files uploaded yet"})]})]})]})]})]})})};function h(){let[e,t]=(0,a.useState)({title:"",description:"",domain:"",level:"Beginner",estimated_hours:""}),[r,i]=(0,a.useState)([]),[l,n]=(0,a.useState)(!1),[d,h]=(0,a.useState)(""),g=(e,r)=>{t(t=>({...t,[e]:r}))},f=async(e,t)=>new Promise((e,r)=>{let s=0,a=setInterval(()=>{t(s+=20),s>=100&&(clearInterval(a),e())},500)}),v=async s=>{s.preventDefault(),n(!0),h("");try{if(0===r.length){h("Please upload at least one PDF file"),n(!1);return}if(r.filter(e=>"completed"!==e.status).length>0){h("Please wait for all files to finish uploading"),n(!1);return}let s=new FormData;if(s.append("title",e.title),s.append("description",e.description),s.append("domain",e.domain),s.append("level",e.level),s.append("estimated_hours",e.estimated_hours),r.forEach((e,t)=>{e.file&&s.append("file",e.file)}),console.log("Submitting form with data:",{...e,files:r.map(e=>({id:e.id,name:e.name,size:e.size,status:e.status}))}),(await fetch("http://*************:5001/api/courses",{method:"POST",body:s})).ok)h("Course uploaded successfully!"),t({title:"",description:"",domain:"",level:"Beginner",estimated_hours:""}),i([]);else throw Error("Upload failed")}catch(e){console.error("Upload error:",e),h("Upload failed. Please try again.")}finally{n(!1)}};return(0,s.jsxs)("div",{className:"max-w-5xl mx-auto bg-white rounded-2xl shadow-lg p-6 mt-4",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-orange-600 mb-6 text-center",children:"Upload New Course"}),(0,s.jsxs)("form",{onSubmit:v,className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(p.J,{htmlFor:"title",children:"Course Title"}),(0,s.jsx)(c,{id:"title",type:"text",value:e.title,onChange:e=>g("title",e.target.value),placeholder:"Enter course title",required:!0,className:"focus:ring-2 focus:ring-orange-200"})]}),(0,s.jsxs)("div",{className:"col-span-2 space-y-2",children:[(0,s.jsx)(p.J,{htmlFor:"description",children:"Description"}),(0,s.jsx)(u,{id:"description",value:e.description,onChange:e=>g("description",e.target.value),placeholder:"Enter course description",rows:3,required:!0,className:"focus:ring-2 focus:ring-orange-200 resize-none"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(p.J,{htmlFor:"domain",children:"Domain"}),(0,s.jsxs)(m.l6,{value:e.domain,onValueChange:e=>g("domain",e),children:[(0,s.jsx)(m.bq,{className:"w-full focus:ring-2 focus:ring-orange-200",children:(0,s.jsx)(m.yv,{placeholder:"Select Domain"})}),(0,s.jsxs)(m.gC,{children:[(0,s.jsx)(m.eb,{value:"Medical",children:"Medicine"}),(0,s.jsx)(m.eb,{value:"Engineering",children:"Engineering"}),(0,s.jsx)(m.eb,{value:"Science",children:"Science"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(p.J,{children:"Level"}),(0,s.jsxs)(m.l6,{value:e.level,onValueChange:e=>g("level",e),children:[(0,s.jsx)(m.bq,{className:"w-full focus:ring-2 focus:ring-orange-200",children:(0,s.jsx)(m.yv,{placeholder:"Select level"})}),(0,s.jsxs)(m.gC,{children:[(0,s.jsx)(m.eb,{value:"Beginner",children:"Beginner"}),(0,s.jsx)(m.eb,{value:"Intermediate",children:"Intermediate"}),(0,s.jsx)(m.eb,{value:"Advanced",children:"Advanced"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(p.J,{htmlFor:"estimated_hours",children:"Estimated Hours"}),(0,s.jsx)(c,{id:"estimated_hours",type:"number",min:0,max:100,value:e.estimated_hours,onChange:e=>g("estimated_hours",e.target.value),placeholder:"0"})]})]}),(0,s.jsxs)("div",{className:"col-span-full space-y-2",children:[(0,s.jsx)(p.J,{children:"Upload PDF File"}),(0,s.jsx)(x,{files:r,onFilesChange:e=>{console.log("Files updated:",e),i(e)},onFileRemove:e=>{console.log("File removed:",e),i(t=>t.filter(t=>t.id!==e))},uploadFunction:f,title:"Upload Course Material",subtitle:"Upload your course PDF file",maxFiles:1,maxFileSize:0x3200000,acceptedFileTypes:[".pdf"],dragText:"Drag and drop your PDF here",browseButtonText:"Browse PDF Files",className:""})]}),(0,s.jsx)("div",{className:"flex justify-center pt-4",children:(0,s.jsx)(o.$,{type:"submit",disabled:l||!(""!==e.title.trim()&&""!==e.description.trim()&&""!==e.domain.trim()&&""!==e.estimated_hours.trim()&&r.length>0&&r.every(e=>"completed"===e.status)),className:"bg-orange-500 hover:bg-orange-600 text-white px-8 py-3 rounded-lg text-lg font-semibold disabled:opacity-50 disabled:cursor-not-allowed",children:l?(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),(0,s.jsx)("span",{children:"Uploading..."})]}):`Upload Course ${r.length>0?`(${r.length} file${r.length>1?"s":""})`:""}`})}),d&&(0,s.jsx)("div",{className:`text-center font-medium mt-4 p-3 rounded-lg ${d.includes("success")||d.includes("uploaded")?"text-green-600 bg-green-50":d.includes("failed")||d.includes("error")?"text-red-600 bg-red-50":"text-blue-600 bg-blue-50"}`,children:d})]})]})}},7910:e=>{"use strict";e.exports=require("stream")},8230:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\app\\\\admin\\\\upload\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\AI-Trainer3\\frontend\\src\\app\\admin\\upload\\page.tsx","default")},8286:(e,t,r)=>{Promise.resolve().then(r.bind(r,8230))},8354:e=>{"use strict";e.exports=require("util")},9021:e=>{"use strict";e.exports=require("fs")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9551:e=>{"use strict";e.exports=require("url")},9646:e=>{"use strict";e.exports=require("child_process")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,773,658,782,578],()=>r(4737));module.exports=s})();