(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[24],{474:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>x});var r=s(5155),a=s(5494),l=s(2115),n=s(7489),i=s(3578);let c=e=>{let{certificate:t}=e;return(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-4 flex items-center space-x-4 border border-gray-200",children:[(0,r.jsx)("div",{className:"flex-shrink-0 bg-purple-50 text-purple-700 w-12 h-12 rounded-full flex items-center justify-center text-xl",children:(0,r.jsx)(n.g,{icon:i.LPI})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h3",{className:"text-base font-semibold text-gray-800",children:t.title}),(0,r.jsxs)("p",{className:"text-gray-600 text-xs mt-1",children:["Domain: ",t.domain," • Level: ",t.level]}),(0,r.jsxs)("p",{className:"text-gray-600 text-xs",children:["Certificate ID: ",t.certificate_id]}),(0,r.jsxs)("p",{className:"text-gray-600 text-xs",children:["Issued: ",new Date(t.issued_date).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})]}),(0,r.jsxs)("p",{className:"text-gray-600 text-xs",children:[t.estimated_hours," hours • ",t.total_modules," modules completed"]})]}),(0,r.jsxs)("div",{className:"flex flex-col items-end space-y-1.5",children:[(0,r.jsx)("span",{className:"inline-block bg-purple-100 text-purple-700 text-xs px-2 py-0.5 rounded-full font-medium",children:"Verified"}),(0,r.jsxs)("div",{className:"flex items-center space-x-1.5",children:[(0,r.jsxs)("button",{className:"bg-gray-100 text-gray-800 px-3 py-1 rounded-md hover:bg-gray-200 transition-colors duration-200 text-xs flex items-center space-x-1",children:[(0,r.jsx)(n.g,{icon:i.pS3}),(0,r.jsx)("span",{children:"View"})]}),(0,r.jsxs)("button",{className:"bg-orange-500 text-white px-3 py-1 rounded-md hover:bg-orange-600 transition-colors duration-200 text-xs flex items-center space-x-1",children:[(0,r.jsx)(n.g,{icon:i.cbP}),(0,r.jsx)("span",{children:"Download"})]}),(0,r.jsxs)("button",{className:"bg-gray-100 text-gray-800 px-3 py-1 rounded-md hover:bg-gray-200 transition-colors duration-200 text-xs flex items-center space-x-1",children:[(0,r.jsx)(n.g,{icon:i.QOt}),(0,r.jsx)("span",{children:"Share"})]})]})]})]})},o=e=>{let{value:t,label:s}=e;return(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-4 text-center border border-orange-200",children:[(0,r.jsx)("p",{className:"text-3xl font-bold text-orange-500",children:t}),(0,r.jsx)("p",{className:"text-gray-800 text-sm mt-1",children:s})]})};function x(){let[e,t]=(0,l.useState)(null),[s,x]=(0,l.useState)(!0),[d,m]=(0,l.useState)(null),[u,f]=(0,l.useState)(null),h=()=>{let e=localStorage.getItem("token");if(e)try{let t=e.split(".")[1].replace(/-/g,"+").replace(/_/g,"/"),s=decodeURIComponent(atob(t).split("").map(function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)}).join(""));return JSON.parse(s)}catch(e){console.error("Error decoding token:",e)}return null};(0,l.useEffect)(()=>{let e=h();e?f(e):window.location.href="/signin"},[]);let g=null==u?void 0:u.id;if((0,l.useEffect)(()=>{let e=async()=>{if(g)try{x(!0);let e=await fetch("http://172.24.175.70:5001"+"/api/users/".concat(g,"/certificates"));if(!e.ok)throw Error("Failed to fetch certificates");let s=await e.json();t(s)}catch(e){m(e instanceof Error?e.message:"An error occurred")}finally{x(!1)}};u&&g&&e()},[g,u]),s)return(0,r.jsxs)("div",{className:"flex flex-col min-h-screen bg-[#fafbfc]",children:[(0,r.jsx)(a.default,{}),(0,r.jsx)("main",{className:"flex-grow container mx-auto px-6 py-10",children:(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"text-lg text-gray-600",children:"Loading certificates..."})})})]});if(d||!e)return(0,r.jsxs)("div",{className:"flex flex-col min-h-screen bg-[#fafbfc]",children:[(0,r.jsx)(a.default,{}),(0,r.jsx)("main",{className:"flex-grow container mx-auto px-6 py-10",children:(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"text-lg text-red-600",children:d||"Failed to load certificates"})})})]});let{certificates:p,statistics:j}=e;return(0,r.jsxs)("div",{className:"flex flex-col min-h-screen bg-[#fafbfc]",children:[(0,r.jsx)(a.default,{}),(0,r.jsxs)("main",{className:"flex-grow container mx-auto px-6 py-10",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-800 mb-2",children:"My Certificates"}),(0,r.jsx)("p",{className:"text-gray-600 text-base mb-6",children:"Download and share your course completion certificates"}),p.length>0?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"space-y-6 mb-8",children:p.map(e=>(0,r.jsx)(c,{certificate:e},e.course_id))}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,r.jsx)(o,{value:j.total_certificates,label:"Certificates Earned"}),(0,r.jsx)(o,{value:j.unique_domains,label:"Different Domains"}),(0,r.jsx)(o,{value:j.total_learning_hours,label:"Learning Hours"})]})]}):(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center",children:[(0,r.jsx)(n.g,{icon:i.LPI,className:"text-gray-400 text-4xl mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No certificates earned yet"}),(0,r.jsx)("p",{className:"text-gray-500 mb-4",children:"Complete courses to earn certificates"}),(0,r.jsx)("a",{href:"/courses",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700 transition-colors",children:"Browse Courses"})]})]})]})}},1987:(e,t,s)=>{Promise.resolve().then(s.bind(s,474))}},e=>{var t=t=>e(e.s=t);e.O(0,[266,298,581,494,441,684,358],()=>t(1987)),_N_E=e.O()}]);