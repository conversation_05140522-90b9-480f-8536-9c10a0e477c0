(()=>{var e={};e.id=871,e.ids=[871],e.modules={13:(e,t,r)=>{"use strict";r.d(t,{J:()=>o});var n=r(687);r(3210);var a=r(8148),s=r(4780);function o({className:e,...t}){return(0,n.jsx)(a.b,{"data-slot":"label",className:(0,s.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(1658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},919:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\app\\\\admin\\\\ai-summarization\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\AI-Trainer3\\frontend\\src\\app\\admin\\ai-summarization\\page.tsx","default")},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1820:e=>{"use strict";e.exports=require("os")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3477:(e,t,r)=>{Promise.resolve().then(r.bind(r,919))},3873:e=>{"use strict";e.exports=require("path")},3997:e=>{"use strict";e.exports=require("tty")},4075:e=>{"use strict";e.exports=require("zlib")},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},5079:(e,t,r)=>{"use strict";r.d(t,{bq:()=>u,eb:()=>m,gC:()=>p,l6:()=>d,yv:()=>c});var n=r(687);r(3210);var a=r(19),s=r(5891),o=r(3964),i=r(3589),l=r(4780);function d({...e}){return(0,n.jsx)(a.bL,{"data-slot":"select",...e})}function c({...e}){return(0,n.jsx)(a.WT,{"data-slot":"select-value",...e})}function u({className:e,size:t="default",children:r,...o}){return(0,n.jsxs)(a.l9,{"data-slot":"select-trigger","data-size":t,className:(0,l.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...o,children:[r,(0,n.jsx)(a.In,{asChild:!0,children:(0,n.jsx)(s.A,{className:"size-4 opacity-50"})})]})}function p({className:e,children:t,position:r="popper",...s}){return(0,n.jsx)(a.ZL,{children:(0,n.jsxs)(a.UC,{"data-slot":"select-content",className:(0,l.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...s,children:[(0,n.jsx)(x,{}),(0,n.jsx)(a.LM,{className:(0,l.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,n.jsx)(h,{})]})})}function m({className:e,children:t,...r}){return(0,n.jsxs)(a.q7,{"data-slot":"select-item",className:(0,l.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...r,children:[(0,n.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,n.jsx)(a.VF,{children:(0,n.jsx)(o.A,{className:"size-4"})})}),(0,n.jsx)(a.p4,{children:t})]})}function x({className:e,...t}){return(0,n.jsx)(a.PP,{"data-slot":"select-scroll-up-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,n.jsx)(i.A,{className:"size-4"})})}function h({className:e,...t}){return(0,n.jsx)(a.wn,{"data-slot":"select-scroll-down-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,n.jsx)(s.A,{className:"size-4"})})}},5333:(e,t,r)=>{Promise.resolve().then(r.bind(r,6535))},5387:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var n=r(5239),a=r(8088),s=r(8170),o=r.n(s),i=r(893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let d={children:["",{children:["admin",{children:["ai-summarization",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,919)),"C:\\Projects\\AI-Trainer3\\frontend\\src\\app\\admin\\ai-summarization\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,8429)),"C:\\Projects\\AI-Trainer3\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Projects\\AI-Trainer3\\frontend\\src\\app\\admin\\ai-summarization\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/ai-summarization/page",pathname:"/admin/ai-summarization",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},6059:(e,t,r)=>{"use strict";r.d(t,{C:()=>o});var n=r(3210),a=r(8599),s=r(6156),o=e=>{let{present:t,children:r}=e,o=function(e){var t,r;let[a,o]=n.useState(),l=n.useRef(null),d=n.useRef(e),c=n.useRef("none"),[u,p]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>r[e][t]??e,t));return n.useEffect(()=>{let e=i(l.current);c.current="mounted"===u?e:"none"},[u]),(0,s.N)(()=>{let t=l.current,r=d.current;if(r!==e){let n=c.current,a=i(t);e?p("MOUNT"):"none"===a||t?.display==="none"?p("UNMOUNT"):r&&n!==a?p("ANIMATION_OUT"):p("UNMOUNT"),d.current=e}},[e,p]),(0,s.N)(()=>{if(a){let e,t=a.ownerDocument.defaultView??window,r=r=>{let n=i(l.current).includes(r.animationName);if(r.target===a&&n&&(p("ANIMATION_END"),!d.current)){let r=a.style.animationFillMode;a.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===a.style.animationFillMode&&(a.style.animationFillMode=r)})}},n=e=>{e.target===a&&(c.current=i(l.current))};return a.addEventListener("animationstart",n),a.addEventListener("animationcancel",r),a.addEventListener("animationend",r),()=>{t.clearTimeout(e),a.removeEventListener("animationstart",n),a.removeEventListener("animationcancel",r),a.removeEventListener("animationend",r)}}p("ANIMATION_END")},[a,p]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:n.useCallback(e=>{l.current=e?getComputedStyle(e):null,o(e)},[])}}(t),l="function"==typeof r?r({present:o.isPresent}):n.Children.only(r),d=(0,a.s)(o.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof r||o.isPresent?n.cloneElement(l,{ref:d}):null};function i(e){return e?.animationName||"none"}o.displayName="Presence"},6535:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>en});var n=r(687),a=r(9523),s=r(13),o=r(5079),i=r(3210),l=r(569),d=r(8599),c=r(1273),u=r(1355),p=r(6963),m=r(8674),x=r(5028),h=r(6059),g=r(4163),f=r(8730),b=r(5551),v=r(9024),[y,j]=(0,c.A)("Tooltip",[m.Bk]),w=(0,m.Bk)(),N="TooltipProvider",C="tooltip.open",[k,T]=y(N),P=e=>{let{__scopeTooltip:t,delayDuration:r=700,skipDelayDuration:a=300,disableHoverableContent:s=!1,children:o}=e,l=i.useRef(!0),d=i.useRef(!1),c=i.useRef(0);return i.useEffect(()=>{let e=c.current;return()=>window.clearTimeout(e)},[]),(0,n.jsx)(k,{scope:t,isOpenDelayedRef:l,delayDuration:r,onOpen:i.useCallback(()=>{window.clearTimeout(c.current),l.current=!1},[]),onClose:i.useCallback(()=>{window.clearTimeout(c.current),c.current=window.setTimeout(()=>l.current=!0,a)},[a]),isPointerInTransitRef:d,onPointerInTransitChange:i.useCallback(e=>{d.current=e},[]),disableHoverableContent:s,children:o})};P.displayName=N;var A="Tooltip",[M,_]=y(A),S=e=>{let{__scopeTooltip:t,children:r,open:a,defaultOpen:s,onOpenChange:o,disableHoverableContent:l,delayDuration:d}=e,c=T(A,e.__scopeTooltip),u=w(t),[x,h]=i.useState(null),g=(0,p.B)(),f=i.useRef(0),v=l??c.disableHoverableContent,y=d??c.delayDuration,j=i.useRef(!1),[N,k]=(0,b.i)({prop:a,defaultProp:s??!1,onChange:e=>{e?(c.onOpen(),document.dispatchEvent(new CustomEvent(C))):c.onClose(),o?.(e)},caller:A}),P=i.useMemo(()=>N?j.current?"delayed-open":"instant-open":"closed",[N]),_=i.useCallback(()=>{window.clearTimeout(f.current),f.current=0,j.current=!1,k(!0)},[k]),S=i.useCallback(()=>{window.clearTimeout(f.current),f.current=0,k(!1)},[k]),E=i.useCallback(()=>{window.clearTimeout(f.current),f.current=window.setTimeout(()=>{j.current=!0,k(!0),f.current=0},y)},[y,k]);return i.useEffect(()=>()=>{f.current&&(window.clearTimeout(f.current),f.current=0)},[]),(0,n.jsx)(m.bL,{...u,children:(0,n.jsx)(M,{scope:t,contentId:g,open:N,stateAttribute:P,trigger:x,onTriggerChange:h,onTriggerEnter:i.useCallback(()=>{c.isOpenDelayedRef.current?E():_()},[c.isOpenDelayedRef,E,_]),onTriggerLeave:i.useCallback(()=>{v?S():(window.clearTimeout(f.current),f.current=0)},[S,v]),onOpen:_,onClose:S,disableHoverableContent:v,children:r})})};S.displayName=A;var E="TooltipTrigger",z=i.forwardRef((e,t)=>{let{__scopeTooltip:r,...a}=e,s=_(E,r),o=T(E,r),c=w(r),u=i.useRef(null),p=(0,d.s)(t,u,s.onTriggerChange),x=i.useRef(!1),h=i.useRef(!1),f=i.useCallback(()=>x.current=!1,[]);return i.useEffect(()=>()=>document.removeEventListener("pointerup",f),[f]),(0,n.jsx)(m.Mz,{asChild:!0,...c,children:(0,n.jsx)(g.sG.button,{"aria-describedby":s.open?s.contentId:void 0,"data-state":s.stateAttribute,...a,ref:p,onPointerMove:(0,l.m)(e.onPointerMove,e=>{"touch"!==e.pointerType&&(h.current||o.isPointerInTransitRef.current||(s.onTriggerEnter(),h.current=!0))}),onPointerLeave:(0,l.m)(e.onPointerLeave,()=>{s.onTriggerLeave(),h.current=!1}),onPointerDown:(0,l.m)(e.onPointerDown,()=>{s.open&&s.onClose(),x.current=!0,document.addEventListener("pointerup",f,{once:!0})}),onFocus:(0,l.m)(e.onFocus,()=>{x.current||s.onOpen()}),onBlur:(0,l.m)(e.onBlur,s.onClose),onClick:(0,l.m)(e.onClick,s.onClose)})})});z.displayName=E;var O="TooltipPortal",[L,R]=y(O,{forceMount:void 0}),I=e=>{let{__scopeTooltip:t,forceMount:r,children:a,container:s}=e,o=_(O,t);return(0,n.jsx)(L,{scope:t,forceMount:r,children:(0,n.jsx)(h.C,{present:r||o.open,children:(0,n.jsx)(x.Z,{asChild:!0,container:s,children:a})})})};I.displayName=O;var q="TooltipContent",D=i.forwardRef((e,t)=>{let r=R(q,e.__scopeTooltip),{forceMount:a=r.forceMount,side:s="top",...o}=e,i=_(q,e.__scopeTooltip);return(0,n.jsx)(h.C,{present:a||i.open,children:i.disableHoverableContent?(0,n.jsx)(W,{side:s,...o,ref:t}):(0,n.jsx)(F,{side:s,...o,ref:t})})}),F=i.forwardRef((e,t)=>{let r=_(q,e.__scopeTooltip),a=T(q,e.__scopeTooltip),s=i.useRef(null),o=(0,d.s)(t,s),[l,c]=i.useState(null),{trigger:u,onClose:p}=r,m=s.current,{onPointerInTransitChange:x}=a,h=i.useCallback(()=>{c(null),x(!1)},[x]),g=i.useCallback((e,t)=>{let r=e.currentTarget,n={x:e.clientX,y:e.clientY},a=function(e,t){let r=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),a=Math.abs(t.right-e.x),s=Math.abs(t.left-e.x);switch(Math.min(r,n,a,s)){case s:return"left";case a:return"right";case r:return"top";case n:return"bottom";default:throw Error("unreachable")}}(n,r.getBoundingClientRect());c(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:1*!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let r=0;r<e.length;r++){let n=e[r];for(;t.length>=2;){let e=t[t.length-1],r=t[t.length-2];if((e.x-r.x)*(n.y-r.y)>=(e.y-r.y)*(n.x-r.x))t.pop();else break}t.push(n)}t.pop();let r=[];for(let t=e.length-1;t>=0;t--){let n=e[t];for(;r.length>=2;){let e=r[r.length-1],t=r[r.length-2];if((e.x-t.x)*(n.y-t.y)>=(e.y-t.y)*(n.x-t.x))r.pop();else break}r.push(n)}return(r.pop(),1===t.length&&1===r.length&&t[0].x===r[0].x&&t[0].y===r[0].y)?t:t.concat(r)}(t)}([...function(e,t,r=5){let n=[];switch(t){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r})}return n}(n,a),...function(e){let{top:t,right:r,bottom:n,left:a}=e;return[{x:a,y:t},{x:r,y:t},{x:r,y:n},{x:a,y:n}]}(t.getBoundingClientRect())])),x(!0)},[x]);return i.useEffect(()=>()=>h(),[h]),i.useEffect(()=>{if(u&&m){let e=e=>g(e,m),t=e=>g(e,u);return u.addEventListener("pointerleave",e),m.addEventListener("pointerleave",t),()=>{u.removeEventListener("pointerleave",e),m.removeEventListener("pointerleave",t)}}},[u,m,g,h]),i.useEffect(()=>{if(l){let e=e=>{let t=e.target,r={x:e.clientX,y:e.clientY},n=u?.contains(t)||m?.contains(t),a=!function(e,t){let{x:r,y:n}=e,a=!1;for(let e=0,s=t.length-1;e<t.length;s=e++){let o=t[e],i=t[s],l=o.x,d=o.y,c=i.x,u=i.y;d>n!=u>n&&r<(c-l)*(n-d)/(u-d)+l&&(a=!a)}return a}(r,l);n?h():a&&(h(),p())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[u,m,l,p,h]),(0,n.jsx)(W,{...e,ref:o})}),[U,B]=y(A,{isInside:!1}),G=(0,f.Dc)("TooltipContent"),W=i.forwardRef((e,t)=>{let{__scopeTooltip:r,children:a,"aria-label":s,onEscapeKeyDown:o,onPointerDownOutside:l,...d}=e,c=_(q,r),p=w(r),{onClose:x}=c;return i.useEffect(()=>(document.addEventListener(C,x),()=>document.removeEventListener(C,x)),[x]),i.useEffect(()=>{if(c.trigger){let e=e=>{let t=e.target;t?.contains(c.trigger)&&x()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[c.trigger,x]),(0,n.jsx)(u.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:o,onPointerDownOutside:l,onFocusOutside:e=>e.preventDefault(),onDismiss:x,children:(0,n.jsxs)(m.UC,{"data-state":c.stateAttribute,...p,...d,ref:t,style:{...d.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,n.jsx)(G,{children:a}),(0,n.jsx)(U,{scope:r,isInside:!0,children:(0,n.jsx)(v.bL,{id:c.contentId,role:"tooltip",children:s||a})})]})})});D.displayName=q;var $="TooltipArrow",J=i.forwardRef((e,t)=>{let{__scopeTooltip:r,...a}=e,s=w(r);return B($,r).isInside?null:(0,n.jsx)(m.i3,{...s,...a,ref:t})});J.displayName=$;var V=r(4780);function H({delayDuration:e=0,...t}){return(0,n.jsx)(P,{"data-slot":"tooltip-provider",delayDuration:e,...t})}function Z({...e}){return(0,n.jsx)(H,{children:(0,n.jsx)(S,{"data-slot":"tooltip",...e})})}function X({...e}){return(0,n.jsx)(z,{"data-slot":"tooltip-trigger",...e})}function Y({className:e,sideOffset:t=0,variant:r="default",children:a,...s}){let o="";switch(r){case"info":o="bg-blue-600 text-white";break;case"success":o="bg-green-600 text-white";break;case"danger":o="bg-red-600 text-white";break;case"warning":o="bg-yellow-400 text-gray-900";break;case"brand":o="bg-brand-blue text-white border border-brand-blue";break;default:o="bg-primary text-primary-foreground"}return(0,n.jsx)(I,{children:(0,n.jsxs)(D,{"data-slot":"tooltip-content",sideOffset:t,className:(0,V.cn)("animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-[--radix-tooltip-content-transform-origin] rounded-md px-3 py-1.5 text-xs text-balance",o,e),...s,children:[a,(0,n.jsx)(J,{className:(0,V.cn)("z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]",o)})]})})}var K=r(3964);let Q=(0,r(2688).A)("check-check",[["path",{d:"M18 6 7 17l-5-5",key:"116fxf"}],["path",{d:"m22 10-7.5 7.5L13 16",key:"ke71qq"}]]);function ee({pages:e,selected:t,onSelect:r}){return(0,n.jsxs)("div",{className:"w-full mb-8",children:[(0,n.jsxs)("h3",{className:" font-semibold text-brand-blue/[85%] mt-2 mb-4",children:["Medical Course Pages (",e.length," pages)"]}),(0,n.jsx)("ul",{className:"h-full space-y-3 overflow-y-auto max-h-[calc(100vh-5%)] pr-2 custom-scrollbar",children:e.map((e,a)=>(0,n.jsx)("li",{children:(0,n.jsxs)("button",{className:`w-full text-left px-5 py-3 rounded-xl border transition-all duration-200 flex flex-col gap-1 shadow-sm 
                ${t===a?"border-orange-500 bg-orange-50 text-orange-800 shadow-md":"border-gray-200 bg-white hover:bg-orange-50 hover:border-orange-300"}
              `,onClick:()=>r(a),children:[(0,n.jsxs)("div",{className:"font-bold text-lg",children:["Page ",e.page]}),(0,n.jsx)("div",{className:"text-sm text-gray-600 truncate",children:e.title}),(0,n.jsx)("span",{className:`inline-block mt-1 text-xs px-3 py-1 rounded-full font-medium 
                ${"Approved"===e.status?"bg-green-100 text-green-700":"bg-orange-100 text-orange-600"}
              `,children:e.status})]})},e.page))})]})}function et({page:e,onChangeSummary:t,onApprove:r,onRegenerate:s,onCustomPrompt:o,onViewOriginal:i,onApproveAll:l,approvedCount:d,totalCount:c,pages:u,selected:p,onSelect:m}){let x=c>0?d/c*100:0;return(0,n.jsxs)("div",{className:"flex-1 flex flex-col bg-white rounded-2xl shadow-xl px-4 py-4 border border-orange-100",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,n.jsxs)("h2",{className:"text-2xl font-bold text-brand-blue flex items-center gap-2",children:[(0,n.jsx)("svg",{className:"w-7 h-7 text-orange-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"})}),(0,n.jsx)("span",{className:"text-brand-blue",children:"AI-Powered Medical Summarization"})]}),(0,n.jsxs)("div",{className:"text-md font-medium text-brand-blue-300",children:["Progress: ",(0,n.jsx)("span",{className:"text-orange-600",children:d})," of ",(0,n.jsx)("span",{className:"text-brand-blue-300",children:c})," approved"]})]}),(0,n.jsx)("div",{className:"w-full bg-brand-blue-300/[20%] rounded-full h-2.5 mb-6",children:(0,n.jsx)("div",{className:"bg-green-500 h-2.5 rounded-full transition-all duration-500 ease-out",style:{width:`${x}%`}})}),(0,n.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,n.jsx)("div",{className:"col-span-2",children:(0,n.jsxs)("div",{className:"flex-1 flex flex-col",children:[(0,n.jsxs)("div",{className:"flex items-center gap-4 mb-4",children:[(0,n.jsx)("h3",{className:"font-bold text-brand-blue-300",children:e.title}),(0,n.jsxs)("div",{className:"flex-1 flex justify-end gap-2",children:[(0,n.jsxs)(Z,{children:[(0,n.jsx)(X,{asChild:!0,children:(0,n.jsx)(a.$,{variant:"outline",className:"px-4 py-2 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-100 text-sm font-medium transition flex items-center gap-1 shadow-sm",onClick:s,children:(0,n.jsxs)("svg",{className:"w-4 h-4 text-gray-600",fill:"none",stroke:"currentColor",strokeWidth:"2",viewBox:"0 0 24 24",children:[(0,n.jsx)("path",{d:"M4 4v5h.582M20 20v-5h-.581"}),(0,n.jsx)("path",{d:"M5.077 19A9 9 0 1 0 6.5 6.5L4 9.5"})]})})}),(0,n.jsx)(Y,{variant:"brand",children:(0,n.jsx)("p",{children:"Regenerate"})})]}),(0,n.jsxs)(Z,{children:[(0,n.jsx)(X,{asChild:!0,children:(0,n.jsx)(a.$,{variant:"outline",className:"px-4 py-2 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-100 text-sm font-medium transition flex items-center gap-1 shadow-sm",onClick:o,children:(0,n.jsxs)("svg",{className:"w-4 h-4 text-gray-600",fill:"none",stroke:"currentColor",strokeWidth:"2",viewBox:"0 0 24 24",children:[(0,n.jsx)("path",{d:"M16.862 3.487a2.6 2.6 0 1 1 3.677 3.677L7.5 20.205l-4.5 1 1-4.5L16.862 3.487Z"}),(0,n.jsx)("path",{d:"M15 6.5 17.5 9"})]})})}),(0,n.jsx)(Y,{variant:"brand",children:(0,n.jsx)("p",{children:"Custom Prompt"})})]}),(0,n.jsxs)(Z,{children:[(0,n.jsx)(X,{asChild:!0,children:(0,n.jsx)(a.$,{variant:"outline",className:"px-4 py-2 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-100 text-sm font-medium transition flex items-center gap-1 shadow-sm",onClick:i,children:(0,n.jsxs)("svg",{className:"w-4 h-4 text-gray-600",fill:"none",stroke:"currentColor",strokeWidth:"2",viewBox:"0 0 24 24",children:[(0,n.jsx)("path",{d:"M1.5 12S5 5 12 5s10.5 7 10.5 7-3.5 7-10.5 7S1.5 12 1.5 12Z"}),(0,n.jsx)("circle",{cx:"12",cy:"12",r:"3"})]})})}),(0,n.jsx)(Y,{variant:"brand",children:(0,n.jsx)("p",{children:"View Original"})})]})]})]}),(0,n.jsxs)("div",{className:"mb-6 flex-1 flex flex-col",children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"AI-Generated Summary"}),(0,n.jsx)("textarea",{className:"w-full border border-gray-200 rounded-lg px-4 py-3 min-h-[150px] focus:ring-2 focus:ring-orange-200 focus:border-orange-300 transition-colors duration-200 resize-y flex-1",value:e.aiSummary,onChange:e=>t(e.target.value)}),(0,n.jsxs)("div",{className:"flex items-center gap-2 mt-4 text-xs text-gray-500",children:[(0,n.jsx)("span",{className:"bg-orange-50 text-orange-600 px-2 py-1 rounded-full font-medium",children:"Generated by Azure OpenAI GPT-4"}),(0,n.jsx)("span",{className:"bg-brand-blue/[25%] text-brand-blue px-2 py-1 rounded-full font-medium",children:"Medical AI Model"})]})]}),(0,n.jsxs)("div",{className:"mb-6 flex-1 flex flex-col",children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Original Content Preview"}),(0,n.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 text-gray-700 text-sm min-h-[150px] overflow-y-auto flex-1 custom-scrollbar",children:e.original})]}),(0,n.jsxs)("div",{className:"flex gap-3 mt-2 justify-start",children:[(0,n.jsxs)(a.$,{variant:"outline",className:"text-brand-blue border-brand-blue hover:bg-brand-blue hover:text-white px-6 py-3 rounded-lg font-semibold shadow-md hover:shadow-lg transition-all duration-200 flex items-center gap-2",onClick:r,children:[(0,n.jsx)(K.A,{className:"w-5 h-5"}),"Approve"]}),(0,n.jsxs)(a.$,{variant:"outline",className:"text-brand-blue border-brand-blue hover:bg-brand-blue hover:text-white px-6 py-3 rounded-lg font-semibold shadow-md hover:shadow-lg transition-all duration-200 flex items-center gap-2",onClick:l,children:[(0,n.jsx)(Q,{className:"w-5 h-5"}),"Approve All (",u.filter(e=>"Pending Review"===e.status).length," remaining)"]})]})]})}),(0,n.jsx)("div",{className:"",children:(0,n.jsx)(ee,{pages:u,selected:p,onSelect:m})})]})]})}function er(){let[e,t]=(0,i.useState)(0),[r,a]=(0,i.useState)([]),[l,d]=(0,i.useState)(!1),[c,u]=(0,i.useState)(""),[p,m]=(0,i.useState)([]),[x,h]=(0,i.useState)(""),[g,f]=(0,i.useState)(!1),[b,v]=(0,i.useState)(!1),[y,j]=(0,i.useState)(""),[w,N]=(0,i.useState)(null),C=async e=>{h(e),f(!0);let r=await fetch("http://172.24.175.70:5001/api/courses/"+e),n=await r.json();a((n.pdf_output||[]).map((e,t)=>({page:e.page||t+1,title:`Page ${e.page||t+1}`,status:"Pending Review",aiSummary:e.imagetext||"",original:e.imagetext||"",imageassestid:e.imageassestid}))),t(0),f(!1),N(n.blob_url||null)},k=()=>{a(e=>e.map(e=>({...e,status:"Approved"})))},T=async()=>{let t=r[e].aiSummary;try{let r=await fetch("http://172.24.175.70:5001/api/rephrase",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({text:t})}),n=await r.json();n&&n.rephrased?a(t=>t.map((t,r)=>r===e?{...t,aiSummary:n.rephrased}:t)):"string"==typeof n&&a(t=>t.map((t,r)=>r===e?{...t,aiSummary:n}:t))}catch(e){alert("Failed to rephrase.")}},P=async()=>{let t=r[e].aiSummary;try{let r=await fetch("http://172.24.175.70:5001/api/customrephrase",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({text:t,prompt:c})}),n=await r.json();n&&n.result?a(t=>t.map((t,r)=>r===e?{...t,aiSummary:n.result}:t)):alert("Failed to get rephrased result.")}catch(e){alert("Failed to get rephrased result.")}d(!1),u("")},A=async()=>{v(!1),k();let e="unknown";try{let t=localStorage.getItem("token");if(t){let r=atob(t.split(".")[1]);e=JSON.parse(r).email||"unknown"}}catch{}try{(await fetch(`http://172.24.175.70:5001/api/courses/${x}/approve`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({content:r.map(e=>({...e,status:"Approved"})),approved_by:e,comment:""})})).ok?(j("Content approved and saved!"),setTimeout(()=>j(""),3e3)):alert("Failed to save approved content!")}catch(e){alert("Failed to save approved content!")}};return(0,n.jsxs)("div",{className:"w-full px-4 py-4 flex flex-col items-stretch justify-start relative",children:[(0,n.jsx)("form",{className:"flex gap-4 items-end py-4",onSubmit:e=>e.preventDefault(),children:(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsx)(s.J,{htmlFor:"course",className:"py-2",children:"Course"}),(0,n.jsxs)(o.l6,{value:x||"",onValueChange:e=>C(e),children:[(0,n.jsx)(o.bq,{className:"w-auto focus:ring-2 focus:ring-orange-200",children:(0,n.jsx)(o.yv,{placeholder:"Select Course"})}),(0,n.jsx)(o.gC,{children:p.map(e=>(0,n.jsxs)(o.eb,{value:e.id.toString(),children:[e.title," (",e.domain,")"]},e.id))})]})]})}),g&&(0,n.jsx)("div",{className:"text-center text-lg text-orange-500",children:"Loading course data..."}),!g&&r.length>0&&(0,n.jsx)(et,{page:r[e],onChangeSummary:t=>{a(r=>r.map((r,n)=>n===e?{...r,aiSummary:t}:r))},onApprove:()=>{a(t=>t.map((t,r)=>r===e?{...t,status:"Approved"}:t))},onRegenerate:T,onCustomPrompt:()=>{d(!0)},onViewOriginal:()=>{w?window.open(w,"_blank"):alert("No original content URL available.")},onApproveAll:()=>{v(!0)},approvedCount:r.filter(e=>"Approved"===e.status).length,totalCount:r.length,pages:r,selected:e,onSelect:t}),l&&(0,n.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,n.jsxs)("div",{className:"bg-white rounded-2xl shadow-2xl p-8 max-w-md w-full animate-fade-in-up relative",children:[(0,n.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-4",children:"Custom AI Prompt"}),(0,n.jsx)("textarea",{className:"w-full border border-gray-300 rounded-lg p-3 min-h-[120px] focus:ring-2 focus:ring-orange-200 focus:border-orange-300 transition-colors duration-200 resize-y",placeholder:"Enter your custom prompt here to regenerate the summary...",value:c,onChange:e=>u(e.target.value)}),(0,n.jsxs)("div",{className:"flex justify-end gap-3 mt-6",children:[(0,n.jsx)("button",{className:"bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-3 rounded-lg font-semibold transition-colors duration-200",onClick:()=>{d(!1),u("")},children:"Cancel"}),(0,n.jsx)("button",{className:"bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-200",onClick:P,children:"Generate"})]})]})}),b&&(0,n.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,n.jsxs)("div",{className:"bg-white rounded-2xl shadow-2xl p-8 max-w-sm w-full animate-fade-in-up relative text-center",children:[(0,n.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-4",children:"Confirm Approval"}),(0,n.jsx)("p",{className:"text-gray-600 mb-6",children:"Are you sure you want to approve all pending summaries for this course? This action cannot be undone."}),(0,n.jsxs)("div",{className:"flex justify-center gap-4",children:[(0,n.jsx)("button",{className:"bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-3 rounded-lg font-semibold transition-colors duration-200",onClick:()=>{v(!1)},children:"Cancel"}),(0,n.jsx)("button",{className:"bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-200",onClick:A,children:"Confirm Approve All"})]})]})}),y&&(0,n.jsx)("div",{className:"fixed bottom-6 right-6 bg-green-500 text-white px-6 py-3 rounded-lg shadow-xl text-lg font-semibold animate-fade-in-right z-50",children:y})]})}function en(){return(0,n.jsx)(er,{})}},7910:e=>{"use strict";e.exports=require("stream")},8354:e=>{"use strict";e.exports=require("util")},9021:e=>{"use strict";e.exports=require("fs")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9551:e=>{"use strict";e.exports=require("url")},9646:e=>{"use strict";e.exports=require("child_process")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[447,773,658,782,578],()=>r(5387));module.exports=n})();