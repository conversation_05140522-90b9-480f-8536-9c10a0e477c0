"use strict";exports.id=782,exports.ids=[782],exports.modules={19:(e,t,n)=>{n.d(t,{UC:()=>ej,In:()=>ek,q7:()=>eD,VF:()=>eW,p4:()=>eI,ZL:()=>eM,bL:()=>eL,wn:()=>eF,PP:()=>eB,l9:()=>eP,WT:()=>eN,LM:()=>eO});var r=n(3210),o=n(1215);function i(e,[t,n]){return Math.min(n,Math.max(t,e))}var l=n(569),a=n(1273),u=n(8599),c=n(8730),s=n(687),d=new WeakMap;function f(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=p(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return -1===n?void 0:e[n]}function p(e){return e!=e||0===e?0:Math.trunc(e)}var h=r.createContext(void 0),m=n(1355),v=n(1359),g=n(2547),y=n(6963),w=n(8674),b=n(5028),x=n(4163),E=n(3495),S=n(5551),C=n(6156),R=n(9024),A=n(3376),T=n(2247),L=[" ","Enter","ArrowUp","ArrowDown"],P=[" ","Enter"],N="Select",[k,M,j]=function(e){let t=e+"CollectionProvider",[n,o]=(0,a.A)(t),[i,l]=n(t,{collectionRef:{current:null},itemMap:new Map}),d=e=>{let{scope:t,children:n}=e,o=r.useRef(null),l=r.useRef(new Map).current;return(0,s.jsx)(i,{scope:t,itemMap:l,collectionRef:o,children:n})};d.displayName=t;let f=e+"CollectionSlot",p=(0,c.TL)(f),h=r.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=l(f,n),i=(0,u.s)(t,o.collectionRef);return(0,s.jsx)(p,{ref:i,children:r})});h.displayName=f;let m=e+"CollectionItemSlot",v="data-radix-collection-item",g=(0,c.TL)(m),y=r.forwardRef((e,t)=>{let{scope:n,children:o,...i}=e,a=r.useRef(null),c=(0,u.s)(t,a),d=l(m,n);return r.useEffect(()=>(d.itemMap.set(a,{ref:a,...i}),()=>void d.itemMap.delete(a))),(0,s.jsx)(g,{...{[v]:""},ref:c,children:o})});return y.displayName=m,[{Provider:d,Slot:h,ItemSlot:y},function(t){let n=l(e+"CollectionConsumer",t);return r.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${v}]`));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},o]}(N),[O,D]=(0,a.A)(N,[j,w.Bk]),I=(0,w.Bk)(),[W,B]=O(N),[F,H]=O(N),_=e=>{let{__scopeSelect:t,children:n,open:o,defaultOpen:i,onOpenChange:l,value:a,defaultValue:u,onValueChange:c,dir:d,name:f,autoComplete:p,disabled:m,required:v,form:g}=e,b=I(t),[x,E]=r.useState(null),[C,R]=r.useState(null),[A,T]=r.useState(!1),L=function(e){let t=r.useContext(h);return e||t||"ltr"}(d),[P,M]=(0,S.i)({prop:o,defaultProp:i??!1,onChange:l,caller:N}),[j,O]=(0,S.i)({prop:a,defaultProp:u,onChange:c,caller:N}),D=r.useRef(null),B=!x||g||!!x.closest("form"),[H,_]=r.useState(new Set),z=Array.from(H).map(e=>e.props.value).join(";");return(0,s.jsx)(w.bL,{...b,children:(0,s.jsxs)(W,{required:v,scope:t,trigger:x,onTriggerChange:E,valueNode:C,onValueNodeChange:R,valueNodeHasChildren:A,onValueNodeHasChildrenChange:T,contentId:(0,y.B)(),value:j,onValueChange:O,open:P,onOpenChange:M,dir:L,triggerPointerDownPosRef:D,disabled:m,children:[(0,s.jsx)(k.Provider,{scope:t,children:(0,s.jsx)(F,{scope:e.__scopeSelect,onNativeOptionAdd:r.useCallback(e=>{_(t=>new Set(t).add(e))},[]),onNativeOptionRemove:r.useCallback(e=>{_(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),B?(0,s.jsxs)(eC,{"aria-hidden":!0,required:v,tabIndex:-1,name:f,autoComplete:p,value:j,onChange:e=>O(e.target.value),disabled:m,form:g,children:[void 0===j?(0,s.jsx)("option",{value:""}):null,Array.from(H)]},z):null]})})};_.displayName=N;var z="SelectTrigger",$=r.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:o=!1,...i}=e,a=I(n),c=B(z,n),d=c.disabled||o,f=(0,u.s)(t,c.onTriggerChange),p=M(n),h=r.useRef("touch"),[m,v,g]=eA(e=>{let t=p().filter(e=>!e.disabled),n=t.find(e=>e.value===c.value),r=eT(t,e,n);void 0!==r&&c.onValueChange(r.value)}),y=e=>{d||(c.onOpenChange(!0),g()),e&&(c.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,s.jsx)(w.Mz,{asChild:!0,...a,children:(0,s.jsx)(x.sG.button,{type:"button",role:"combobox","aria-controls":c.contentId,"aria-expanded":c.open,"aria-required":c.required,"aria-autocomplete":"none",dir:c.dir,"data-state":c.open?"open":"closed",disabled:d,"data-disabled":d?"":void 0,"data-placeholder":eR(c.value)?"":void 0,...i,ref:f,onClick:(0,l.m)(i.onClick,e=>{e.currentTarget.focus(),"mouse"!==h.current&&y(e)}),onPointerDown:(0,l.m)(i.onPointerDown,e=>{h.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(y(e),e.preventDefault())}),onKeyDown:(0,l.m)(i.onKeyDown,e=>{let t=""!==m.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||v(e.key),(!t||" "!==e.key)&&L.includes(e.key)&&(y(),e.preventDefault())})})})});$.displayName=z;var V="SelectValue",G=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:i,placeholder:l="",...a}=e,c=B(V,n),{onValueNodeHasChildrenChange:d}=c,f=void 0!==i,p=(0,u.s)(t,c.onValueNodeChange);return(0,C.N)(()=>{d(f)},[d,f]),(0,s.jsx)(x.sG.span,{...a,ref:p,style:{pointerEvents:"none"},children:eR(c.value)?(0,s.jsx)(s.Fragment,{children:l}):i})});G.displayName=V;var K=r.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,s.jsx)(x.sG.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});K.displayName="SelectIcon";var q=e=>(0,s.jsx)(b.Z,{asChild:!0,...e});q.displayName="SelectPortal";var U="SelectContent",Y=r.forwardRef((e,t)=>{let n=B(U,e.__scopeSelect),[i,l]=r.useState();return((0,C.N)(()=>{l(new DocumentFragment)},[]),n.open)?(0,s.jsx)(J,{...e,ref:t}):i?o.createPortal((0,s.jsx)(X,{scope:e.__scopeSelect,children:(0,s.jsx)(k.Slot,{scope:e.__scopeSelect,children:(0,s.jsx)("div",{children:e.children})})}),i):null});Y.displayName=U;var[X,Z]=O(U),Q=(0,c.TL)("SelectContent.RemoveScroll"),J=r.forwardRef((e,t)=>{let{__scopeSelect:n,position:o="item-aligned",onCloseAutoFocus:i,onEscapeKeyDown:a,onPointerDownOutside:c,side:d,sideOffset:f,align:p,alignOffset:h,arrowPadding:y,collisionBoundary:w,collisionPadding:b,sticky:x,hideWhenDetached:E,avoidCollisions:S,...C}=e,R=B(U,n),[L,P]=r.useState(null),[N,k]=r.useState(null),j=(0,u.s)(t,e=>P(e)),[O,D]=r.useState(null),[I,W]=r.useState(null),F=M(n),[H,_]=r.useState(!1),z=r.useRef(!1);r.useEffect(()=>{if(L)return(0,A.Eq)(L)},[L]),(0,v.Oh)();let $=r.useCallback(e=>{let[t,...n]=F().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(n?.scrollIntoView({block:"nearest"}),n===t&&N&&(N.scrollTop=0),n===r&&N&&(N.scrollTop=N.scrollHeight),n?.focus(),document.activeElement!==o))return},[F,N]),V=r.useCallback(()=>$([O,L]),[$,O,L]);r.useEffect(()=>{H&&V()},[H,V]);let{onOpenChange:G,triggerPointerDownPosRef:K}=R;r.useEffect(()=>{if(L){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(K.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(K.current?.y??0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():L.contains(n.target)||G(!1),document.removeEventListener("pointermove",t),K.current=null};return null!==K.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[L,G,K]),r.useEffect(()=>{let e=()=>G(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[G]);let[q,Y]=eA(e=>{let t=F().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=eT(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),Z=r.useCallback((e,t,n)=>{let r=!z.current&&!n;(void 0!==R.value&&R.value===t||r)&&(D(e),r&&(z.current=!0))},[R.value]),J=r.useCallback(()=>L?.focus(),[L]),en=r.useCallback((e,t,n)=>{let r=!z.current&&!n;(void 0!==R.value&&R.value===t||r)&&W(e)},[R.value]),er="popper"===o?et:ee,eo=er===et?{side:d,sideOffset:f,align:p,alignOffset:h,arrowPadding:y,collisionBoundary:w,collisionPadding:b,sticky:x,hideWhenDetached:E,avoidCollisions:S}:{};return(0,s.jsx)(X,{scope:n,content:L,viewport:N,onViewportChange:k,itemRefCallback:Z,selectedItem:O,onItemLeave:J,itemTextRefCallback:en,focusSelectedItem:V,selectedItemText:I,position:o,isPositioned:H,searchRef:q,children:(0,s.jsx)(T.A,{as:Q,allowPinchZoom:!0,children:(0,s.jsx)(g.n,{asChild:!0,trapped:R.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,l.m)(i,e=>{R.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,s.jsx)(m.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:c,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>R.onOpenChange(!1),children:(0,s.jsx)(er,{role:"listbox",id:R.contentId,"data-state":R.open?"open":"closed",dir:R.dir,onContextMenu:e=>e.preventDefault(),...C,...eo,onPlaced:()=>_(!0),ref:j,style:{display:"flex",flexDirection:"column",outline:"none",...C.style},onKeyDown:(0,l.m)(C.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||Y(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=F().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>$(t)),e.preventDefault()}})})})})})})});J.displayName="SelectContentImpl";var ee=r.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:o,...l}=e,a=B(U,n),c=Z(U,n),[d,f]=r.useState(null),[p,h]=r.useState(null),m=(0,u.s)(t,e=>h(e)),v=M(n),g=r.useRef(!1),y=r.useRef(!0),{viewport:w,selectedItem:b,selectedItemText:E,focusSelectedItem:S}=c,R=r.useCallback(()=>{if(a.trigger&&a.valueNode&&d&&p&&w&&b&&E){let e=a.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),n=a.valueNode.getBoundingClientRect(),r=E.getBoundingClientRect();if("rtl"!==a.dir){let o=r.left-t.left,l=n.left-o,a=e.left-l,u=e.width+a,c=Math.max(u,t.width),s=i(l,[10,Math.max(10,window.innerWidth-10-c)]);d.style.minWidth=u+"px",d.style.left=s+"px"}else{let o=t.right-r.right,l=window.innerWidth-n.right-o,a=window.innerWidth-e.right-l,u=e.width+a,c=Math.max(u,t.width),s=i(l,[10,Math.max(10,window.innerWidth-10-c)]);d.style.minWidth=u+"px",d.style.right=s+"px"}let l=v(),u=window.innerHeight-20,c=w.scrollHeight,s=window.getComputedStyle(p),f=parseInt(s.borderTopWidth,10),h=parseInt(s.paddingTop,10),m=parseInt(s.borderBottomWidth,10),y=f+h+c+parseInt(s.paddingBottom,10)+m,x=Math.min(5*b.offsetHeight,y),S=window.getComputedStyle(w),C=parseInt(S.paddingTop,10),R=parseInt(S.paddingBottom,10),A=e.top+e.height/2-10,T=b.offsetHeight/2,L=f+h+(b.offsetTop+T);if(L<=A){let e=l.length>0&&b===l[l.length-1].ref.current;d.style.bottom="0px";let t=Math.max(u-A,T+(e?R:0)+(p.clientHeight-w.offsetTop-w.offsetHeight)+m);d.style.height=L+t+"px"}else{let e=l.length>0&&b===l[0].ref.current;d.style.top="0px";let t=Math.max(A,f+w.offsetTop+(e?C:0)+T);d.style.height=t+(y-L)+"px",w.scrollTop=L-A+w.offsetTop}d.style.margin="10px 0",d.style.minHeight=x+"px",d.style.maxHeight=u+"px",o?.(),requestAnimationFrame(()=>g.current=!0)}},[v,a.trigger,a.valueNode,d,p,w,b,E,a.dir,o]);(0,C.N)(()=>R(),[R]);let[A,T]=r.useState();(0,C.N)(()=>{p&&T(window.getComputedStyle(p).zIndex)},[p]);let L=r.useCallback(e=>{e&&!0===y.current&&(R(),S?.(),y.current=!1)},[R,S]);return(0,s.jsx)(en,{scope:n,contentWrapper:d,shouldExpandOnScrollRef:g,onScrollButtonChange:L,children:(0,s.jsx)("div",{ref:f,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:A},children:(0,s.jsx)(x.sG.div,{...l,ref:m,style:{boxSizing:"border-box",maxHeight:"100%",...l.style}})})})});ee.displayName="SelectItemAlignedPosition";var et=r.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...i}=e,l=I(n);return(0,s.jsx)(w.UC,{...l,...i,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});et.displayName="SelectPopperPosition";var[en,er]=O(U,{}),eo="SelectViewport",ei=r.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:o,...i}=e,a=Z(eo,n),c=er(eo,n),d=(0,u.s)(t,a.onViewportChange),f=r.useRef(0);return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),(0,s.jsx)(k.Slot,{scope:n,children:(0,s.jsx)(x.sG.div,{"data-radix-select-viewport":"",role:"presentation",...i,ref:d,style:{position:"relative",flex:1,overflow:"hidden auto",...i.style},onScroll:(0,l.m)(i.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=c;if(r?.current&&n){let e=Math.abs(f.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let i=o+e,l=Math.min(r,i),a=i-l;n.style.height=l+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}f.current=t.scrollTop})})})]})});ei.displayName=eo;var el="SelectGroup",[ea,eu]=O(el);r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=(0,y.B)();return(0,s.jsx)(ea,{scope:n,id:o,children:(0,s.jsx)(x.sG.div,{role:"group","aria-labelledby":o,...r,ref:t})})}).displayName=el;var ec="SelectLabel";r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=eu(ec,n);return(0,s.jsx)(x.sG.div,{id:o.id,...r,ref:t})}).displayName=ec;var es="SelectItem",[ed,ef]=O(es),ep=r.forwardRef((e,t)=>{let{__scopeSelect:n,value:o,disabled:i=!1,textValue:a,...c}=e,d=B(es,n),f=Z(es,n),p=d.value===o,[h,m]=r.useState(a??""),[v,g]=r.useState(!1),w=(0,u.s)(t,e=>f.itemRefCallback?.(e,o,i)),b=(0,y.B)(),E=r.useRef("touch"),S=()=>{i||(d.onValueChange(o),d.onOpenChange(!1))};if(""===o)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,s.jsx)(ed,{scope:n,value:o,disabled:i,textId:b,isSelected:p,onItemTextChange:r.useCallback(e=>{m(t=>t||(e?.textContent??"").trim())},[]),children:(0,s.jsx)(k.ItemSlot,{scope:n,value:o,disabled:i,textValue:h,children:(0,s.jsx)(x.sG.div,{role:"option","aria-labelledby":b,"data-highlighted":v?"":void 0,"aria-selected":p&&v,"data-state":p?"checked":"unchecked","aria-disabled":i||void 0,"data-disabled":i?"":void 0,tabIndex:i?void 0:-1,...c,ref:w,onFocus:(0,l.m)(c.onFocus,()=>g(!0)),onBlur:(0,l.m)(c.onBlur,()=>g(!1)),onClick:(0,l.m)(c.onClick,()=>{"mouse"!==E.current&&S()}),onPointerUp:(0,l.m)(c.onPointerUp,()=>{"mouse"===E.current&&S()}),onPointerDown:(0,l.m)(c.onPointerDown,e=>{E.current=e.pointerType}),onPointerMove:(0,l.m)(c.onPointerMove,e=>{E.current=e.pointerType,i?f.onItemLeave?.():"mouse"===E.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,l.m)(c.onPointerLeave,e=>{e.currentTarget===document.activeElement&&f.onItemLeave?.()}),onKeyDown:(0,l.m)(c.onKeyDown,e=>{(f.searchRef?.current===""||" "!==e.key)&&(P.includes(e.key)&&S()," "===e.key&&e.preventDefault())})})})})});ep.displayName=es;var eh="SelectItemText",em=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:i,style:l,...a}=e,c=B(eh,n),d=Z(eh,n),f=ef(eh,n),p=H(eh,n),[h,m]=r.useState(null),v=(0,u.s)(t,e=>m(e),f.onItemTextChange,e=>d.itemTextRefCallback?.(e,f.value,f.disabled)),g=h?.textContent,y=r.useMemo(()=>(0,s.jsx)("option",{value:f.value,disabled:f.disabled,children:g},f.value),[f.disabled,f.value,g]),{onNativeOptionAdd:w,onNativeOptionRemove:b}=p;return(0,C.N)(()=>(w(y),()=>b(y)),[w,b,y]),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(x.sG.span,{id:f.textId,...a,ref:v}),f.isSelected&&c.valueNode&&!c.valueNodeHasChildren?o.createPortal(a.children,c.valueNode):null]})});em.displayName=eh;var ev="SelectItemIndicator",eg=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return ef(ev,n).isSelected?(0,s.jsx)(x.sG.span,{"aria-hidden":!0,...r,ref:t}):null});eg.displayName=ev;var ey="SelectScrollUpButton",ew=r.forwardRef((e,t)=>{let n=Z(ey,e.__scopeSelect),o=er(ey,e.__scopeSelect),[i,l]=r.useState(!1),a=(0,u.s)(t,o.onScrollButtonChange);return(0,C.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){l(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,s.jsx)(eE,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ew.displayName=ey;var eb="SelectScrollDownButton",ex=r.forwardRef((e,t)=>{let n=Z(eb,e.__scopeSelect),o=er(eb,e.__scopeSelect),[i,l]=r.useState(!1),a=(0,u.s)(t,o.onScrollButtonChange);return(0,C.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;l(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,s.jsx)(eE,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});ex.displayName=eb;var eE=r.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:o,...i}=e,a=Z("SelectScrollButton",n),u=r.useRef(null),c=M(n),d=r.useCallback(()=>{null!==u.current&&(window.clearInterval(u.current),u.current=null)},[]);return r.useEffect(()=>()=>d(),[d]),(0,C.N)(()=>{let e=c().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[c]),(0,s.jsx)(x.sG.div,{"aria-hidden":!0,...i,ref:t,style:{flexShrink:0,...i.style},onPointerDown:(0,l.m)(i.onPointerDown,()=>{null===u.current&&(u.current=window.setInterval(o,50))}),onPointerMove:(0,l.m)(i.onPointerMove,()=>{a.onItemLeave?.(),null===u.current&&(u.current=window.setInterval(o,50))}),onPointerLeave:(0,l.m)(i.onPointerLeave,()=>{d()})})});r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,s.jsx)(x.sG.div,{"aria-hidden":!0,...r,ref:t})}).displayName="SelectSeparator";var eS="SelectArrow";r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=I(n),i=B(eS,n),l=Z(eS,n);return i.open&&"popper"===l.position?(0,s.jsx)(w.i3,{...o,...r,ref:t}):null}).displayName=eS;var eC=r.forwardRef(({__scopeSelect:e,value:t,...n},o)=>{let i=r.useRef(null),l=(0,u.s)(o,i),a=function(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(t);return r.useEffect(()=>{let e=i.current;if(!e)return;let n=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(a!==t&&n){let r=new Event("change",{bubbles:!0});n.call(e,t),e.dispatchEvent(r)}},[a,t]),(0,s.jsx)(x.sG.select,{...n,style:{...R.Qg,...n.style},ref:l,defaultValue:t})});function eR(e){return""===e||void 0===e}function eA(e){let t=(0,E.c)(e),n=r.useRef(""),o=r.useRef(0),i=r.useCallback(e=>{let r=n.current+e;t(r),function e(t){n.current=t,window.clearTimeout(o.current),""!==t&&(o.current=window.setTimeout(()=>e(""),1e3))}(r)},[t]),l=r.useCallback(()=>{n.current="",window.clearTimeout(o.current)},[]);return r.useEffect(()=>()=>window.clearTimeout(o.current),[]),[n,i,l]}function eT(e,t,n){var r,o;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,l=n?e.indexOf(n):-1,a=(r=e,o=Math.max(l,0),r.map((e,t)=>r[(o+t)%r.length]));1===i.length&&(a=a.filter(e=>e!==n));let u=a.find(e=>e.textValue.toLowerCase().startsWith(i.toLowerCase()));return u!==n?u:void 0}eC.displayName="SelectBubbleInput";var eL=_,eP=$,eN=G,ek=K,eM=q,ej=Y,eO=ei,eD=ep,eI=em,eW=eg,eB=ew,eF=ex},569:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},1273:(e,t,n)=>{n.d(t,{A:()=>l,q:()=>i});var r=n(3210),o=n(687);function i(e,t){let n=r.createContext(t),i=e=>{let{children:t,...i}=e,l=r.useMemo(()=>i,Object.values(i));return(0,o.jsx)(n.Provider,{value:l,children:t})};return i.displayName=e+"Provider",[i,function(o){let i=r.useContext(n);if(i)return i;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function l(e,t=[]){let n=[],i=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return i.scopeName=e,[function(t,i){let l=r.createContext(i),a=n.length;n=[...n,i];let u=t=>{let{scope:n,children:i,...u}=t,c=n?.[e]?.[a]||l,s=r.useMemo(()=>u,Object.values(u));return(0,o.jsx)(c.Provider,{value:s,children:i})};return u.displayName=t+"Provider",[u,function(n,o){let u=o?.[e]?.[a]||l,c=r.useContext(u);if(c)return c;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(i,...t)]}},1355:(e,t,n)=>{n.d(t,{qW:()=>f});var r,o=n(3210),i=n(569),l=n(4163),a=n(8599),u=n(3495),c=n(687),s="dismissableLayer.update",d=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:f,onPointerDownOutside:m,onFocusOutside:v,onInteractOutside:g,onDismiss:y,...w}=e,b=o.useContext(d),[x,E]=o.useState(null),S=x?.ownerDocument??globalThis?.document,[,C]=o.useState({}),R=(0,a.s)(t,e=>E(e)),A=Array.from(b.layers),[T]=[...b.layersWithOutsidePointerEventsDisabled].slice(-1),L=A.indexOf(T),P=x?A.indexOf(x):-1,N=b.layersWithOutsidePointerEventsDisabled.size>0,k=P>=L,M=function(e,t=globalThis?.document){let n=(0,u.c)(e),r=o.useRef(!1),i=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!r.current){let r=function(){h("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",i.current),i.current=r,t.addEventListener("click",i.current,{once:!0})):r()}else t.removeEventListener("click",i.current);r.current=!1},o=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(o),t.removeEventListener("pointerdown",e),t.removeEventListener("click",i.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,n=[...b.branches].some(e=>e.contains(t));k&&!n&&(m?.(e),g?.(e),e.defaultPrevented||y?.())},S),j=function(e,t=globalThis?.document){let n=(0,u.c)(e),r=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!r.current&&h("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;![...b.branches].some(e=>e.contains(t))&&(v?.(e),g?.(e),e.defaultPrevented||y?.())},S);return!function(e,t=globalThis?.document){let n=(0,u.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{P===b.layers.size-1&&(f?.(e),!e.defaultPrevented&&y&&(e.preventDefault(),y()))},S),o.useEffect(()=>{if(x)return n&&(0===b.layersWithOutsidePointerEventsDisabled.size&&(r=S.body.style.pointerEvents,S.body.style.pointerEvents="none"),b.layersWithOutsidePointerEventsDisabled.add(x)),b.layers.add(x),p(),()=>{n&&1===b.layersWithOutsidePointerEventsDisabled.size&&(S.body.style.pointerEvents=r)}},[x,S,n,b]),o.useEffect(()=>()=>{x&&(b.layers.delete(x),b.layersWithOutsidePointerEventsDisabled.delete(x),p())},[x,b]),o.useEffect(()=>{let e=()=>C({});return document.addEventListener(s,e),()=>document.removeEventListener(s,e)},[]),(0,c.jsx)(l.sG.div,{...w,ref:R,style:{pointerEvents:N?k?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.m)(e.onFocusCapture,j.onFocusCapture),onBlurCapture:(0,i.m)(e.onBlurCapture,j.onBlurCapture),onPointerDownCapture:(0,i.m)(e.onPointerDownCapture,M.onPointerDownCapture)})});function p(){let e=new CustomEvent(s);document.dispatchEvent(e)}function h(e,t,n,{discrete:r}){let o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?(0,l.hO)(o,i):o.dispatchEvent(i)}f.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(d),r=o.useRef(null),i=(0,a.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,c.jsx)(l.sG.div,{...e,ref:i})}).displayName="DismissableLayerBranch"},1359:(e,t,n)=>{n.d(t,{Oh:()=>i});var r=n(3210),o=0;function i(){r.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??l()),document.body.insertAdjacentElement("beforeend",e[1]??l()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function l(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},2247:(e,t,n)=>{n.d(t,{A:()=>K});var r,o,i=function(){return(i=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function l(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var a=("function"==typeof SuppressedError&&SuppressedError,n(3210)),u="right-scroll-bar-position",c="width-before-scroll-bar";function s(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var d="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,f=new WeakMap;function p(e){return e}var h=function(e){void 0===e&&(e={});var t,n,r,o,l=(t=null,void 0===n&&(n=p),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var i=function(){var n=t;t=[],n.forEach(e)},l=function(){return Promise.resolve().then(i)};l(),r={push:function(e){t.push(e),l()},filter:function(e){return t=t.filter(e),r}}}});return l.options=i({async:!0,ssr:!1},e),l}(),m=function(){},v=a.forwardRef(function(e,t){var n,r,o,u,c=a.useRef(null),p=a.useState({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:m}),v=p[0],g=p[1],y=e.forwardProps,w=e.children,b=e.className,x=e.removeScrollBar,E=e.enabled,S=e.shards,C=e.sideCar,R=e.noRelative,A=e.noIsolation,T=e.inert,L=e.allowPinchZoom,P=e.as,N=e.gapMode,k=l(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),M=(n=[c,t],r=function(e){return n.forEach(function(t){return s(t,e)})},(o=(0,a.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,u=o.facade,d(function(){var e=f.get(u);if(e){var t=new Set(e),r=new Set(n),o=u.current;t.forEach(function(e){r.has(e)||s(e,null)}),r.forEach(function(e){t.has(e)||s(e,o)})}f.set(u,n)},[n]),u),j=i(i({},k),v);return a.createElement(a.Fragment,null,E&&a.createElement(C,{sideCar:h,removeScrollBar:x,shards:S,noRelative:R,noIsolation:A,inert:T,setCallbacks:g,allowPinchZoom:!!L,lockRef:c,gapMode:N}),y?a.cloneElement(a.Children.only(w),i(i({},j),{ref:M})):a.createElement(void 0===P?"div":P,i({},j,{className:b,ref:M}),w))});v.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},v.classNames={fullWidth:c,zeroRight:u};var g=function(e){var t=e.sideCar,n=l(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return a.createElement(r,i({},n))};g.isSideCarExport=!0;var y=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,l;(i=t).styleSheet?i.styleSheet.cssText=r:i.appendChild(document.createTextNode(r)),l=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(l)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},w=function(){var e=y();return function(t,n){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},b=function(){var e=w();return function(t){return e(t.styles,t.dynamic),null}},x={left:0,top:0,right:0,gap:0},E=function(e){return parseInt(e||"",10)||0},S=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[E(n),E(r),E(o)]},C=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return x;var t=S(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},R=b(),A="data-scroll-locked",T=function(e,t,n,r){var o=e.left,i=e.top,l=e.right,a=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(a,"px ").concat(r,";\n  }\n  body[").concat(A,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(l,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(a,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(u," {\n    right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(c," {\n    margin-right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(c," .").concat(c," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(A,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(a,"px;\n  }\n")},L=function(){var e=parseInt(document.body.getAttribute(A)||"0",10);return isFinite(e)?e:0},P=function(){a.useEffect(function(){return document.body.setAttribute(A,(L()+1).toString()),function(){var e=L()-1;e<=0?document.body.removeAttribute(A):document.body.setAttribute(A,e.toString())}},[])},N=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;P();var i=a.useMemo(function(){return C(o)},[o]);return a.createElement(R,{styles:T(i,!t,o,n?"":"!important")})},k=!1;if("undefined"!=typeof window)try{var M=Object.defineProperty({},"passive",{get:function(){return k=!0,!0}});window.addEventListener("test",M,M),window.removeEventListener("test",M,M)}catch(e){k=!1}var j=!!k&&{passive:!1},O=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},D=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),I(e,r)){var o=W(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},I=function(e,t){return"v"===e?O(t,"overflowY"):O(t,"overflowX")},W=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},B=function(e,t,n,r,o){var i,l=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),a=l*r,u=n.target,c=t.contains(u),s=!1,d=a>0,f=0,p=0;do{if(!u)break;var h=W(e,u),m=h[0],v=h[1]-h[2]-l*m;(m||v)&&I(e,u)&&(f+=v,p+=m);var g=u.parentNode;u=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&a>f)?s=!0:!d&&(o&&1>Math.abs(p)||!o&&-a>p)&&(s=!0),s},F=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},H=function(e){return[e.deltaX,e.deltaY]},_=function(e){return e&&"current"in e?e.current:e},z=0,$=[];let V=(r=function(e){var t=a.useRef([]),n=a.useRef([0,0]),r=a.useRef(),o=a.useState(z++)[0],i=a.useState(b)[0],l=a.useRef(e);a.useEffect(function(){l.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(_),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=a.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!l.current.allowPinchZoom;var o,i=F(e),a=n.current,u="deltaX"in e?e.deltaX:a[0]-i[0],c="deltaY"in e?e.deltaY:a[1]-i[1],s=e.target,d=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=D(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=D(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||c)&&(r.current=o),!o)return!0;var p=r.current||o;return B(p,t,e,"h"===p?u:c,!0)},[]),c=a.useCallback(function(e){if($.length&&$[$.length-1]===i){var n="deltaY"in e?H(e):F(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(l.current.shards||[]).map(_).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!l.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=a.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=a.useCallback(function(e){n.current=F(e),r.current=void 0},[]),f=a.useCallback(function(t){s(t.type,H(t),t.target,u(t,e.lockRef.current))},[]),p=a.useCallback(function(t){s(t.type,F(t),t.target,u(t,e.lockRef.current))},[]);a.useEffect(function(){return $.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",c,j),document.addEventListener("touchmove",c,j),document.addEventListener("touchstart",d,j),function(){$=$.filter(function(e){return e!==i}),document.removeEventListener("wheel",c,j),document.removeEventListener("touchmove",c,j),document.removeEventListener("touchstart",d,j)}},[]);var h=e.removeScrollBar,m=e.inert;return a.createElement(a.Fragment,null,m?a.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?a.createElement(N,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},h.useMedium(r),g);var G=a.forwardRef(function(e,t){return a.createElement(v,i({},e,{ref:t,sideCar:V}))});G.classNames=v.classNames;let K=G},2547:(e,t,n)=>{n.d(t,{n:()=>d});var r=n(3210),o=n(8599),i=n(4163),l=n(3495),a=n(687),u="focusScope.autoFocusOnMount",c="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},d=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:d=!1,onMountAutoFocus:v,onUnmountAutoFocus:g,...y}=e,[w,b]=r.useState(null),x=(0,l.c)(v),E=(0,l.c)(g),S=r.useRef(null),C=(0,o.s)(t,e=>b(e)),R=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(d){let e=function(e){if(R.paused||!w)return;let t=e.target;w.contains(t)?S.current=t:h(S.current,{select:!0})},t=function(e){if(R.paused||!w)return;let t=e.relatedTarget;null!==t&&(w.contains(t)||h(S.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&h(w)});return w&&n.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[d,w,R.paused]),r.useEffect(()=>{if(w){m.add(R);let e=document.activeElement;if(!w.contains(e)){let t=new CustomEvent(u,s);w.addEventListener(u,x),w.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(h(r,{select:t}),document.activeElement!==n)return}(f(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&h(w))}return()=>{w.removeEventListener(u,x),setTimeout(()=>{let t=new CustomEvent(c,s);w.addEventListener(c,E),w.dispatchEvent(t),t.defaultPrevented||h(e??document.body,{select:!0}),w.removeEventListener(c,E),m.remove(R)},0)}}},[w,x,E,R]);let A=r.useCallback(e=>{if(!n&&!d||R.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,i]=function(e){let t=f(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&i?e.shiftKey||r!==i?e.shiftKey&&r===o&&(e.preventDefault(),n&&h(i,{select:!0})):(e.preventDefault(),n&&h(o,{select:!0})):r===t&&e.preventDefault()}},[n,d,R.paused]);return(0,a.jsx)(i.sG.div,{tabIndex:-1,...y,ref:C,onKeyDown:A})});function f(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function h(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}d.displayName="FocusScope";var m=function(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),(e=v(e,t)).unshift(t)},remove(t){e=v(e,t),e[0]?.resume()}}}();function v(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},3376:(e,t,n)=>{n.d(t,{Eq:()=>s});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,i=new WeakMap,l={},a=0,u=function(e){return e&&(e.host||u(e.parentNode))},c=function(e,t,n,r){var c=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=u(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});l[n]||(l[n]=new WeakMap);var s=l[n],d=[],f=new Set,p=new Set(c),h=function(e){!e||f.has(e)||(f.add(e),h(e.parentNode))};c.forEach(h);var m=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))m(e);else try{var t=e.getAttribute(r),l=null!==t&&"false"!==t,a=(o.get(e)||0)+1,u=(s.get(e)||0)+1;o.set(e,a),s.set(e,u),d.push(e),1===a&&l&&i.set(e,!0),1===u&&e.setAttribute(n,"true"),l||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return m(t),f.clear(),a++,function(){d.forEach(function(e){var t=o.get(e)-1,l=s.get(e)-1;o.set(e,t),s.set(e,l),t||(i.has(e)||e.removeAttribute(r),i.delete(e)),l||e.removeAttribute(n)}),--a||(o=new WeakMap,o=new WeakMap,i=new WeakMap,l={})}},s=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),i=t||r(e);return i?(o.push.apply(o,Array.from(i.querySelectorAll("[aria-live], script"))),c(o,i,n,"aria-hidden")):function(){return null}}},3495:(e,t,n)=>{n.d(t,{c:()=>o});var r=n(3210);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},3589:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(2688).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},3964:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(2688).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},4163:(e,t,n)=>{n.d(t,{hO:()=>u,sG:()=>a});var r=n(3210),o=n(1215),i=n(8730),l=n(687),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,i.TL)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(o?n:t,{...i,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function u(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},5028:(e,t,n)=>{n.d(t,{Z:()=>u});var r=n(3210),o=n(1215),i=n(4163),l=n(6156),a=n(687),u=r.forwardRef((e,t)=>{let{container:n,...u}=e,[c,s]=r.useState(!1);(0,l.N)(()=>s(!0),[]);let d=n||c&&globalThis?.document?.body;return d?o.createPortal((0,a.jsx)(i.sG.div,{...u,ref:t}),d):null});u.displayName="Portal"},5551:(e,t,n)=>{n.d(t,{i:()=>a});var r,o=n(3210),i=n(6156),l=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||i.N;function a({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[i,a,u]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),i=o.useRef(n),a=o.useRef(t);return l(()=>{a.current=t},[t]),o.useEffect(()=>{i.current!==n&&(a.current?.(n),i.current=n)},[n,i]),[n,r,a]}({defaultProp:t,onChange:n}),c=void 0!==e,s=c?e:i;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,r])}return[s,o.useCallback(t=>{if(c){let n="function"==typeof t?t(e):t;n!==e&&u.current?.(n)}else a(t)},[c,e,a,u])]}Symbol("RADIX:SYNC_STATE")},5891:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(2688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},6156:(e,t,n)=>{n.d(t,{N:()=>o});var r=n(3210),o=globalThis?.document?r.useLayoutEffect:()=>{}},6963:(e,t,n)=>{n.d(t,{B:()=>u});var r,o=n(3210),i=n(6156),l=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),a=0;function u(e){let[t,n]=o.useState(l());return(0,i.N)(()=>{e||n(e=>e??String(a++))},[e]),e||(t?`radix-${t}`:"")}},8148:(e,t,n)=>{n.d(t,{b:()=>a});var r=n(3210),o=n(4163),i=n(687),l=r.forwardRef((e,t)=>(0,i.jsx)(o.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var a=l},8674:(e,t,n)=>{n.d(t,{Mz:()=>eY,i3:()=>eZ,UC:()=>eX,bL:()=>eU,Bk:()=>eM});var r=n(3210);let o=["top","right","bottom","left"],i=Math.min,l=Math.max,a=Math.round,u=Math.floor,c=e=>({x:e,y:e}),s={left:"right",right:"left",bottom:"top",top:"bottom"},d={start:"end",end:"start"};function f(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function v(e){return"y"===e?"height":"width"}function g(e){return["top","bottom"].includes(p(e))?"y":"x"}function y(e){return e.replace(/start|end/g,e=>d[e])}function w(e){return e.replace(/left|right|bottom|top/g,e=>s[e])}function b(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function x(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function E(e,t,n){let r,{reference:o,floating:i}=e,l=g(t),a=m(g(t)),u=v(a),c=p(t),s="y"===l,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,y=o[u]/2-i[u]/2;switch(c){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-i.width,y:f};break;default:r={x:o.x,y:o.y}}switch(h(t)){case"start":r[a]-=y*(n&&s?-1:1);break;case"end":r[a]+=y*(n&&s?-1:1)}return r}let S=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,a=i.filter(Boolean),u=await (null==l.isRTL?void 0:l.isRTL(t)),c=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:d}=E(c,r,u),f=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:i,fn:m}=a[n],{x:v,y:g,data:y,reset:w}=await m({x:s,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:c,platform:l,elements:{reference:e,floating:t}});s=null!=v?v:s,d=null!=g?g:d,p={...p,[i]:{...p[i],...y}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(c=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:d}=E(c,f,u)),n=-1)}return{x:s,y:d,placement:f,strategy:o,middlewareData:p}};async function C(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:l,elements:a,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:d="floating",altBoundary:p=!1,padding:h=0}=f(t,e),m=b(h),v=a[p?"floating"===d?"reference":"floating":d],g=x(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(v)))||n?v:v.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:c,rootBoundary:s,strategy:u})),y="floating"===d?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,w=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),E=await (null==i.isElement?void 0:i.isElement(w))&&await (null==i.getScale?void 0:i.getScale(w))||{x:1,y:1},S=x(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:y,offsetParent:w,strategy:u}):y);return{top:(g.top-S.top+m.top)/E.y,bottom:(S.bottom-g.bottom+m.bottom)/E.y,left:(g.left-S.left+m.left)/E.x,right:(S.right-g.right+m.right)/E.x}}function R(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function A(e){return o.some(t=>e[t]>=0)}async function T(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),l=p(n),a=h(n),u="y"===g(n),c=["left","top"].includes(l)?-1:1,s=i&&u?-1:1,d=f(t,e),{mainAxis:m,crossAxis:v,alignmentAxis:y}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&"number"==typeof y&&(v="end"===a?-1*y:y),u?{x:v*s,y:m*c}:{x:m*c,y:v*s}}function L(){return"undefined"!=typeof window}function P(e){return M(e)?(e.nodeName||"").toLowerCase():"#document"}function N(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function k(e){var t;return null==(t=(M(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function M(e){return!!L()&&(e instanceof Node||e instanceof N(e).Node)}function j(e){return!!L()&&(e instanceof Element||e instanceof N(e).Element)}function O(e){return!!L()&&(e instanceof HTMLElement||e instanceof N(e).HTMLElement)}function D(e){return!!L()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof N(e).ShadowRoot)}function I(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=_(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function W(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function B(e){let t=F(),n=j(e)?_(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function F(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function H(e){return["html","body","#document"].includes(P(e))}function _(e){return N(e).getComputedStyle(e)}function z(e){return j(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function $(e){if("html"===P(e))return e;let t=e.assignedSlot||e.parentNode||D(e)&&e.host||k(e);return D(t)?t.host:t}function V(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=$(t);return H(n)?t.ownerDocument?t.ownerDocument.body:t.body:O(n)&&I(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=N(o);if(i){let e=G(l);return t.concat(l,l.visualViewport||[],I(o)?o:[],e&&n?V(e):[])}return t.concat(o,V(o,[],n))}function G(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function K(e){let t=_(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=O(e),i=o?e.offsetWidth:n,l=o?e.offsetHeight:r,u=a(n)!==i||a(r)!==l;return u&&(n=i,r=l),{width:n,height:r,$:u}}function q(e){return j(e)?e:e.contextElement}function U(e){let t=q(e);if(!O(t))return c(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=K(t),l=(i?a(n.width):n.width)/r,u=(i?a(n.height):n.height)/o;return l&&Number.isFinite(l)||(l=1),u&&Number.isFinite(u)||(u=1),{x:l,y:u}}let Y=c(0);function X(e){let t=N(e);return F()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:Y}function Z(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),l=q(e),a=c(1);t&&(r?j(r)&&(a=U(r)):a=U(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===N(l))&&o)?X(l):c(0),s=(i.left+u.x)/a.x,d=(i.top+u.y)/a.y,f=i.width/a.x,p=i.height/a.y;if(l){let e=N(l),t=r&&j(r)?N(r):r,n=e,o=G(n);for(;o&&r&&t!==n;){let e=U(o),t=o.getBoundingClientRect(),r=_(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;s*=e.x,d*=e.y,f*=e.x,p*=e.y,s+=i,d+=l,o=G(n=N(o))}}return x({width:f,height:p,x:s,y:d})}function Q(e,t){let n=z(e).scrollLeft;return t?t.left+n:Z(k(e)).left+n}function J(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:Q(e,r)),y:r.top+t.scrollTop}}function ee(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=N(e),r=k(e),o=n.visualViewport,i=r.clientWidth,l=r.clientHeight,a=0,u=0;if(o){i=o.width,l=o.height;let e=F();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,u=o.offsetTop)}return{width:i,height:l,x:a,y:u}}(e,n);else if("document"===t)r=function(e){let t=k(e),n=z(e),r=e.ownerDocument.body,o=l(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=l(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+Q(e),u=-n.scrollTop;return"rtl"===_(r).direction&&(a+=l(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:a,y:u}}(k(e));else if(j(t))r=function(e,t){let n=Z(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=O(e)?U(e):c(1),l=e.clientWidth*i.x,a=e.clientHeight*i.y;return{width:l,height:a,x:o*i.x,y:r*i.y}}(t,n);else{let n=X(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return x(r)}function et(e){return"static"===_(e).position}function en(e,t){if(!O(e)||"fixed"===_(e).position)return null;if(t)return t(e);let n=e.offsetParent;return k(e)===n&&(n=n.ownerDocument.body),n}function er(e,t){let n=N(e);if(W(e))return n;if(!O(e)){let t=$(e);for(;t&&!H(t);){if(j(t)&&!et(t))return t;t=$(t)}return n}let r=en(e,t);for(;r&&["table","td","th"].includes(P(r))&&et(r);)r=en(r,t);return r&&H(r)&&et(r)&&!B(r)?n:r||function(e){let t=$(e);for(;O(t)&&!H(t);){if(B(t))return t;if(W(t))break;t=$(t)}return null}(e)||n}let eo=async function(e){let t=this.getOffsetParent||er,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=O(t),o=k(t),i="fixed"===n,l=Z(e,!0,i,t),a={scrollLeft:0,scrollTop:0},u=c(0);if(r||!r&&!i)if(("body"!==P(t)||I(o))&&(a=z(t)),r){let e=Z(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=Q(o));i&&!r&&o&&(u.x=Q(o));let s=!o||r||i?c(0):J(o,a);return{x:l.left+a.scrollLeft-u.x-s.x,y:l.top+a.scrollTop-u.y-s.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ei={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,l=k(r),a=!!t&&W(t.floating);if(r===l||a&&i)return n;let u={scrollLeft:0,scrollTop:0},s=c(1),d=c(0),f=O(r);if((f||!f&&!i)&&(("body"!==P(r)||I(l))&&(u=z(r)),O(r))){let e=Z(r);s=U(r),d.x=e.x+r.clientLeft,d.y=e.y+r.clientTop}let p=!l||f||i?c(0):J(l,u,!0);return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-u.scrollLeft*s.x+d.x+p.x,y:n.y*s.y-u.scrollTop*s.y+d.y+p.y}},getDocumentElement:k,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,a=[..."clippingAncestors"===n?W(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=V(e,[],!1).filter(e=>j(e)&&"body"!==P(e)),o=null,i="fixed"===_(e).position,l=i?$(e):e;for(;j(l)&&!H(l);){let t=_(l),n=B(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||I(l)&&!n&&function e(t,n){let r=$(t);return!(r===n||!j(r)||H(r))&&("fixed"===_(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):o=t,l=$(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],u=a[0],c=a.reduce((e,n)=>{let r=ee(t,n,o);return e.top=l(r.top,e.top),e.right=i(r.right,e.right),e.bottom=i(r.bottom,e.bottom),e.left=l(r.left,e.left),e},ee(t,u,o));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}},getOffsetParent:er,getElementRects:eo,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=K(e);return{width:t,height:n}},getScale:U,isElement:j,isRTL:function(e){return"rtl"===_(e).direction}};function el(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ea=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:a,platform:u,elements:c,middlewareData:s}=t,{element:d,padding:p=0}=f(e,t)||{};if(null==d)return{};let y=b(p),w={x:n,y:r},x=m(g(o)),E=v(x),S=await u.getDimensions(d),C="y"===x,R=C?"clientHeight":"clientWidth",A=a.reference[E]+a.reference[x]-w[x]-a.floating[E],T=w[x]-a.reference[x],L=await (null==u.getOffsetParent?void 0:u.getOffsetParent(d)),P=L?L[R]:0;P&&await (null==u.isElement?void 0:u.isElement(L))||(P=c.floating[R]||a.floating[E]);let N=P/2-S[E]/2-1,k=i(y[C?"top":"left"],N),M=i(y[C?"bottom":"right"],N),j=P-S[E]-M,O=P/2-S[E]/2+(A/2-T/2),D=l(k,i(O,j)),I=!s.arrow&&null!=h(o)&&O!==D&&a.reference[E]/2-(O<k?k:M)-S[E]/2<0,W=I?O<k?O-k:O-j:0;return{[x]:w[x]+W,data:{[x]:D,centerOffset:O-D-W,...I&&{alignmentOffset:W}},reset:I}}}),eu=(e,t,n)=>{let r=new Map,o={platform:ei,...n},i={...o.platform,_c:r};return S(e,t,{...o,platform:i})};var ec=n(1215),es="undefined"!=typeof document?r.useLayoutEffect:function(){};function ed(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!ed(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!ed(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ef(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ep(e,t){let n=ef(e);return Math.round(t*n)/n}function eh(e){let t=r.useRef(e);return es(()=>{t.current=e}),t}let em=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ea({element:n.current,padding:r}).fn(t):{}:n?ea({element:n,padding:r}).fn(t):{}}}),ev=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:l,middlewareData:a}=t,u=await T(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:l}}}}}(e),options:[e,t]}),eg=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:a=!0,crossAxis:u=!1,limiter:c={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=f(e,t),d={x:n,y:r},h=await C(t,s),v=g(p(o)),y=m(v),w=d[y],b=d[v];if(a){let e="y"===y?"top":"left",t="y"===y?"bottom":"right",n=w+h[e],r=w-h[t];w=l(n,i(w,r))}if(u){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",n=b+h[e],r=b-h[t];b=l(n,i(b,r))}let x=c.fn({...t,[y]:w,[v]:b});return{...x,data:{x:x.x-n,y:x.y-r,enabled:{[y]:a,[v]:u}}}}}}(e),options:[e,t]}),ey=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:l}=t,{offset:a=0,mainAxis:u=!0,crossAxis:c=!0}=f(e,t),s={x:n,y:r},d=g(o),h=m(d),v=s[h],y=s[d],w=f(a,t),b="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(u){let e="y"===h?"height":"width",t=i.reference[h]-i.floating[e]+b.mainAxis,n=i.reference[h]+i.reference[e]-b.mainAxis;v<t?v=t:v>n&&(v=n)}if(c){var x,E;let e="y"===h?"width":"height",t=["top","left"].includes(p(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(x=l.offset)?void 0:x[d])||0)+(t?0:b.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(E=l.offset)?void 0:E[d])||0)-(t?b.crossAxis:0);y<n?y=n:y>r&&(y=r)}return{[h]:v,[d]:y}}}}(e),options:[e,t]}),ew=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,l;let{placement:a,middlewareData:u,rects:c,initialPlacement:s,platform:d,elements:b}=t,{mainAxis:x=!0,crossAxis:E=!0,fallbackPlacements:S,fallbackStrategy:R="bestFit",fallbackAxisSideDirection:A="none",flipAlignment:T=!0,...L}=f(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let P=p(a),N=g(s),k=p(s)===s,M=await (null==d.isRTL?void 0:d.isRTL(b.floating)),j=S||(k||!T?[w(s)]:function(e){let t=w(e);return[y(e),t,y(t)]}(s)),O="none"!==A;!S&&O&&j.push(...function(e,t,n,r){let o=h(e),i=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(p(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(y)))),i}(s,T,A,M));let D=[s,...j],I=await C(t,L),W=[],B=(null==(r=u.flip)?void 0:r.overflows)||[];if(x&&W.push(I[P]),E){let e=function(e,t,n){void 0===n&&(n=!1);let r=h(e),o=m(g(e)),i=v(o),l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=w(l)),[l,w(l)]}(a,c,M);W.push(I[e[0]],I[e[1]])}if(B=[...B,{placement:a,overflows:W}],!W.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=D[e];if(t&&("alignment"!==E||N===g(t)||B.every(e=>e.overflows[0]>0&&g(e.placement)===N)))return{data:{index:e,overflows:B},reset:{placement:t}};let n=null==(i=B.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(R){case"bestFit":{let e=null==(l=B.filter(e=>{if(O){let t=g(e.placement);return t===N||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=s}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),eb=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,a,{placement:u,rects:c,platform:s,elements:d}=t,{apply:m=()=>{},...v}=f(e,t),y=await C(t,v),w=p(u),b=h(u),x="y"===g(u),{width:E,height:S}=c.floating;"top"===w||"bottom"===w?(o=w,a=b===(await (null==s.isRTL?void 0:s.isRTL(d.floating))?"start":"end")?"left":"right"):(a=w,o="end"===b?"top":"bottom");let R=S-y.top-y.bottom,A=E-y.left-y.right,T=i(S-y[o],R),L=i(E-y[a],A),P=!t.middlewareData.shift,N=T,k=L;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(k=A),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(N=R),P&&!b){let e=l(y.left,0),t=l(y.right,0),n=l(y.top,0),r=l(y.bottom,0);x?k=E-2*(0!==e||0!==t?e+t:l(y.left,y.right)):N=S-2*(0!==n||0!==r?n+r:l(y.top,y.bottom))}await m({...t,availableWidth:k,availableHeight:N});let M=await s.getDimensions(d.floating);return E!==M.width||S!==M.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),ex=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=f(e,t);switch(r){case"referenceHidden":{let e=R(await C(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:A(e)}}}case"escaped":{let e=R(await C(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:A(e)}}}default:return{}}}}}(e),options:[e,t]}),eE=(e,t)=>({...em(e),options:[e,t]});var eS=n(4163),eC=n(687),eR=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,eC.jsx)(eS.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eC.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eR.displayName="Arrow";var eA=n(8599),eT=n(1273),eL=n(3495),eP=n(6156),eN="Popper",[ek,eM]=(0,eT.A)(eN),[ej,eO]=ek(eN),eD=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,eC.jsx)(ej,{scope:t,anchor:o,onAnchorChange:i,children:n})};eD.displayName=eN;var eI="PopperAnchor",eW=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...i}=e,l=eO(eI,n),a=r.useRef(null),u=(0,eA.s)(t,a);return r.useEffect(()=>{l.onAnchorChange(o?.current||a.current)}),o?null:(0,eC.jsx)(eS.sG.div,{...i,ref:u})});eW.displayName=eI;var eB="PopperContent",[eF,eH]=ek(eB),e_=r.forwardRef((e,t)=>{let{__scopePopper:n,side:o="bottom",sideOffset:a=0,align:c="center",alignOffset:s=0,arrowPadding:d=0,avoidCollisions:f=!0,collisionBoundary:p=[],collisionPadding:h=0,sticky:m="partial",hideWhenDetached:v=!1,updatePositionStrategy:g="optimized",onPlaced:y,...w}=e,b=eO(eB,n),[x,E]=r.useState(null),S=(0,eA.s)(t,e=>E(e)),[C,R]=r.useState(null),A=function(e){let[t,n]=r.useState(void 0);return(0,eP.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(C),T=A?.width??0,L=A?.height??0,P="number"==typeof h?h:{top:0,right:0,bottom:0,left:0,...h},N=Array.isArray(p)?p:[p],M=N.length>0,j={padding:P,boundary:N.filter(eG),altBoundary:M},{refs:O,floatingStyles:D,placement:I,isPositioned:W,middlewareData:B}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:i,elements:{reference:l,floating:a}={},transform:u=!0,whileElementsMounted:c,open:s}=e,[d,f]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(o);ed(p,o)||h(o);let[m,v]=r.useState(null),[g,y]=r.useState(null),w=r.useCallback(e=>{e!==S.current&&(S.current=e,v(e))},[]),b=r.useCallback(e=>{e!==C.current&&(C.current=e,y(e))},[]),x=l||m,E=a||g,S=r.useRef(null),C=r.useRef(null),R=r.useRef(d),A=null!=c,T=eh(c),L=eh(i),P=eh(s),N=r.useCallback(()=>{if(!S.current||!C.current)return;let e={placement:t,strategy:n,middleware:p};L.current&&(e.platform=L.current),eu(S.current,C.current,e).then(e=>{let t={...e,isPositioned:!1!==P.current};k.current&&!ed(R.current,t)&&(R.current=t,ec.flushSync(()=>{f(t)}))})},[p,t,n,L,P]);es(()=>{!1===s&&R.current.isPositioned&&(R.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[s]);let k=r.useRef(!1);es(()=>(k.current=!0,()=>{k.current=!1}),[]),es(()=>{if(x&&(S.current=x),E&&(C.current=E),x&&E){if(T.current)return T.current(x,E,N);N()}},[x,E,N,T,A]);let M=r.useMemo(()=>({reference:S,floating:C,setReference:w,setFloating:b}),[w,b]),j=r.useMemo(()=>({reference:x,floating:E}),[x,E]),O=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!j.floating)return e;let t=ep(j.floating,d.x),r=ep(j.floating,d.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...ef(j.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,j.floating,d.x,d.y]);return r.useMemo(()=>({...d,update:N,refs:M,elements:j,floatingStyles:O}),[d,N,M,j,O])}({strategy:"fixed",placement:o+("center"!==c?"-"+c:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:a=!0,ancestorResize:c=!0,elementResize:s="function"==typeof ResizeObserver,layoutShift:d="function"==typeof IntersectionObserver,animationFrame:f=!1}=r,p=q(e),h=a||c?[...p?V(p):[],...V(t)]:[];h.forEach(e=>{a&&e.addEventListener("scroll",n,{passive:!0}),c&&e.addEventListener("resize",n)});let m=p&&d?function(e,t){let n,r=null,o=k(e);function a(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function c(s,d){void 0===s&&(s=!1),void 0===d&&(d=1),a();let f=e.getBoundingClientRect(),{left:p,top:h,width:m,height:v}=f;if(s||t(),!m||!v)return;let g=u(h),y=u(o.clientWidth-(p+m)),w={rootMargin:-g+"px "+-y+"px "+-u(o.clientHeight-(h+v))+"px "+-u(p)+"px",threshold:l(0,i(1,d))||1},b=!0;function x(t){let r=t[0].intersectionRatio;if(r!==d){if(!b)return c();r?c(!1,r):n=setTimeout(()=>{c(!1,1e-7)},1e3)}1!==r||el(f,e.getBoundingClientRect())||c(),b=!1}try{r=new IntersectionObserver(x,{...w,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(x,w)}r.observe(e)}(!0),a}(p,n):null,v=-1,g=null;s&&(g=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),n()}),p&&!f&&g.observe(p),g.observe(t));let y=f?Z(e):null;return f&&function t(){let r=Z(e);y&&!el(y,r)&&n(),y=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{a&&e.removeEventListener("scroll",n),c&&e.removeEventListener("resize",n)}),null==m||m(),null==(e=g)||e.disconnect(),g=null,f&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===g}),elements:{reference:b.anchor},middleware:[ev({mainAxis:a+L,alignmentAxis:s}),f&&eg({mainAxis:!0,crossAxis:!1,limiter:"partial"===m?ey():void 0,...j}),f&&ew({...j}),eb({...j,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:o,height:i}=t.reference,l=e.floating.style;l.setProperty("--radix-popper-available-width",`${n}px`),l.setProperty("--radix-popper-available-height",`${r}px`),l.setProperty("--radix-popper-anchor-width",`${o}px`),l.setProperty("--radix-popper-anchor-height",`${i}px`)}}),C&&eE({element:C,padding:d}),eK({arrowWidth:T,arrowHeight:L}),v&&ex({strategy:"referenceHidden",...j})]}),[F,H]=eq(I),_=(0,eL.c)(y);(0,eP.N)(()=>{W&&_?.()},[W,_]);let z=B.arrow?.x,$=B.arrow?.y,G=B.arrow?.centerOffset!==0,[K,U]=r.useState();return(0,eP.N)(()=>{x&&U(window.getComputedStyle(x).zIndex)},[x]),(0,eC.jsx)("div",{ref:O.setFloating,"data-radix-popper-content-wrapper":"",style:{...D,transform:W?D.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:K,"--radix-popper-transform-origin":[B.transformOrigin?.x,B.transformOrigin?.y].join(" "),...B.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eC.jsx)(eF,{scope:n,placedSide:F,onArrowChange:R,arrowX:z,arrowY:$,shouldHideArrow:G,children:(0,eC.jsx)(eS.sG.div,{"data-side":F,"data-align":H,...w,ref:S,style:{...w.style,animation:W?void 0:"none"}})})})});e_.displayName=eB;var ez="PopperArrow",e$={top:"bottom",right:"left",bottom:"top",left:"right"},eV=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=eH(ez,n),i=e$[o.placedSide];return(0,eC.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eC.jsx)(eR,{...r,ref:t,style:{...r.style,display:"block"}})})});function eG(e){return null!==e}eV.displayName=ez;var eK=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,l=i?0:e.arrowWidth,a=i?0:e.arrowHeight,[u,c]=eq(n),s={start:"0%",center:"50%",end:"100%"}[c],d=(o.arrow?.x??0)+l/2,f=(o.arrow?.y??0)+a/2,p="",h="";return"bottom"===u?(p=i?s:`${d}px`,h=`${-a}px`):"top"===u?(p=i?s:`${d}px`,h=`${r.floating.height+a}px`):"right"===u?(p=`${-a}px`,h=i?s:`${f}px`):"left"===u&&(p=`${r.floating.width+a}px`,h=i?s:`${f}px`),{data:{x:p,y:h}}}});function eq(e){let[t,n="center"]=e.split("-");return[t,n]}var eU=eD,eY=eW,eX=e_,eZ=eV},9024:(e,t,n)=>{n.d(t,{Qg:()=>l,bL:()=>u});var r=n(3210),o=n(4163),i=n(687),l=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),a=r.forwardRef((e,t)=>(0,i.jsx)(o.sG.span,{...e,ref:t,style:{...l,...e.style}}));a.displayName="VisuallyHidden";var u=a}};