"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/courses/[id]/page",{

/***/ "(app-pages-browser)/./src/components/QnAWithHeyGenModal.tsx":
/*!***********************************************!*\
  !*** ./src/components/QnAWithHeyGenModal.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var livekit_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! livekit-client */ \"(app-pages-browser)/./node_modules/livekit-client/dist/livekit-client.esm.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst QnAWithHeyGenModal = (param)=>{\n    let { isOpen, onClose, courseId } = param;\n    _s();\n    // Configuration - now using backend endpoints\n    const BACKEND_URL = \"http://172.24.175.70:5001\";\n    // State variables\n    const [sessionInfo, setSessionInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [sessionToken, setSessionToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [room, setRoom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mediaStream, setMediaStream] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [webSocket, setWebSocket] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // const [avatarID, setAvatarID] = useState('Wayne_20240711');\n    const [avatarID, setAvatarID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('Pedro_Chair_Sitting_public');\n    const [voiceID, setVoiceID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [taskInput, setTaskInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [statusMessages, setStatusMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isStarted, setIsStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAutoStarting, setIsAutoStarting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Voice input states\n    const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [recognition, setRecognition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isVoiceSupported, setIsVoiceSupported] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const mediaElementRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const statusElementRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Helper function to update status with enhanced styling\n    const updateStatus = function(message) {\n        let type = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'info';\n        const timestamp = new Date().toLocaleTimeString();\n        const newMessage = {\n            message,\n            type,\n            timestamp\n        };\n        setStatusMessages((prev)=>[\n                ...prev,\n                newMessage\n            ]);\n    };\n    // Auto-scroll status to bottom\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QnAWithHeyGenModal.useEffect\": ()=>{\n            if (statusElementRef.current) {\n                statusElementRef.current.scrollTop = statusElementRef.current.scrollHeight;\n            }\n        }\n    }[\"QnAWithHeyGenModal.useEffect\"], [\n        statusMessages\n    ]);\n    // Initialize status messages and auto-start avatar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QnAWithHeyGenModal.useEffect\": ()=>{\n            if (isOpen) {\n                setStatusMessages([\n                    {\n                        message: \"Welcome to AI Avatar Assistant!\",\n                        type: \"system\",\n                        timestamp: new Date().toLocaleTimeString()\n                    },\n                    {\n                        message: \"Auto-starting avatar session...\",\n                        type: \"info\",\n                        timestamp: new Date().toLocaleTimeString()\n                    },\n                    {\n                        message: \"Please wait while we initialize your AI assistant\",\n                        type: \"info\",\n                        timestamp: new Date().toLocaleTimeString()\n                    }\n                ]);\n                // Set auto-starting state\n                setIsAutoStarting(true);\n                // Auto-start the avatar session after a brief delay to ensure component is ready\n                const autoStartTimer = setTimeout({\n                    \"QnAWithHeyGenModal.useEffect.autoStartTimer\": async ()=>{\n                        try {\n                            await handleStart();\n                            setIsAutoStarting(false);\n                        } catch (error) {\n                            console.error(\"Auto-start failed:\", error);\n                            setIsAutoStarting(false);\n                        }\n                    }\n                }[\"QnAWithHeyGenModal.useEffect.autoStartTimer\"], 1000); // 1 second delay\n                // Cleanup timer if component unmounts or modal closes\n                return ({\n                    \"QnAWithHeyGenModal.useEffect\": ()=>{\n                        clearTimeout(autoStartTimer);\n                        setIsAutoStarting(false);\n                    }\n                })[\"QnAWithHeyGenModal.useEffect\"];\n            } else {\n                // Reset states when modal closes\n                setIsAutoStarting(false);\n            }\n        }\n    }[\"QnAWithHeyGenModal.useEffect\"], [\n        isOpen\n    ]);\n    // Initialize voice recognition\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QnAWithHeyGenModal.useEffect\": ()=>{\n            if (true) {\n                // Check for Web Speech API support\n                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n                if (SpeechRecognition) {\n                    setIsVoiceSupported(true);\n                    const recognitionInstance = new SpeechRecognition();\n                    // Configure recognition\n                    recognitionInstance.continuous = false;\n                    recognitionInstance.interimResults = false;\n                    recognitionInstance.lang = 'en-US';\n                    // Handle results\n                    recognitionInstance.onresult = ({\n                        \"QnAWithHeyGenModal.useEffect\": (event)=>{\n                            const transcript = event.results[0][0].transcript;\n                            setTaskInput(transcript);\n                            updateStatus('\\uD83C\\uDFA4 Voice input: \"'.concat(transcript, '\"'), \"success\");\n                        }\n                    })[\"QnAWithHeyGenModal.useEffect\"];\n                    // Handle errors\n                    recognitionInstance.onerror = ({\n                        \"QnAWithHeyGenModal.useEffect\": (event)=>{\n                            updateStatus(\"Voice recognition error: \".concat(event.error), \"error\");\n                            setIsRecording(false);\n                        }\n                    })[\"QnAWithHeyGenModal.useEffect\"];\n                    // Handle end\n                    recognitionInstance.onend = ({\n                        \"QnAWithHeyGenModal.useEffect\": ()=>{\n                            setIsRecording(false);\n                        }\n                    })[\"QnAWithHeyGenModal.useEffect\"];\n                    setRecognition(recognitionInstance);\n                } else {\n                    setIsVoiceSupported(false);\n                    updateStatus(\"Voice input not supported in this browser\", \"warning\");\n                }\n            }\n        }\n    }[\"QnAWithHeyGenModal.useEffect\"], []);\n    // Get session token\n    const getSessionToken = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(BACKEND_URL, \"/api/heygen/token\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const data = await response.json();\n            if (!data.success || !data.token) {\n                throw new Error('Invalid token data received from server');\n            }\n            setSessionToken(data.token);\n            updateStatus(\"Session token obtained successfully\", \"success\");\n            return data.token;\n        } catch (error) {\n            updateStatus(\"Failed to get session token: \".concat(error.message), \"error\");\n            throw error;\n        }\n    };\n    // Connect WebSocket\n    const connectWebSocket = async (sessionId, token)=>{\n        try {\n            if (!token) {\n                throw new Error('No session token available for WebSocket connection');\n            }\n            const params = new URLSearchParams({\n                session_id: sessionId,\n                session_token: token,\n                silence_response: 'false',\n                opening_text: \"Hello, how can I help you?\",\n                stt_language: \"en\"\n            });\n            const wsUrl = \"wss://api.heygen.com/v1/ws/streaming.chat?\".concat(params);\n            const ws = new WebSocket(wsUrl);\n            setWebSocket(ws);\n            ws.addEventListener(\"message\", (event)=>{\n                const eventData = JSON.parse(event.data);\n                console.log(\"Raw WebSocket event:\", eventData);\n            });\n            ws.addEventListener(\"error\", (error)=>{\n                updateStatus(\"WebSocket connection error\", \"error\");\n                console.error(\"WebSocket error:\", error);\n            });\n            ws.addEventListener(\"open\", ()=>{\n                updateStatus(\"WebSocket connected successfully\", \"success\");\n            });\n        } catch (error) {\n            updateStatus(\"WebSocket connection failed: \".concat(error.message), \"error\");\n            throw error;\n        }\n    };\n    // Create new session\n    const createNewSession = async ()=>{\n        try {\n            // Always get a fresh token to avoid state timing issues\n            const token = await getSessionToken();\n            updateStatus(\"Debug: Creating session with token: \".concat(token ? 'Token exists' : 'No token'), \"info\");\n            const response = await fetch(\"\".concat(BACKEND_URL, \"/api/heygen/session/new\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    token: token,\n                    avatarID: avatarID,\n                    voiceID: voiceID\n                })\n            });\n            updateStatus(\"Debug: Create session response status: \".concat(response.status), \"info\");\n            if (!response.ok) {\n                const errorText = await response.text();\n                updateStatus(\"Debug: Create session error: \".concat(errorText), \"error\");\n                throw new Error(\"HTTP error! status: \".concat(response.status, \" - \").concat(errorText));\n            }\n            const data = await response.json();\n            if (!data.success || !data.sessionData || !data.sessionData.session_id) {\n                throw new Error('Invalid session data received from server');\n            }\n            updateStatus(\"Debug: Session data keys: \".concat(Object.keys(data.sessionData).join(', ')), \"info\");\n            // Set session info and return it for immediate use\n            setSessionInfo(data.sessionData);\n            // Create LiveKit Room\n            const livekitRoom = new livekit_client__WEBPACK_IMPORTED_MODULE_2__.Room({\n                adaptiveStream: true,\n                dynacast: true,\n                videoCaptureDefaults: {\n                    resolution: livekit_client__WEBPACK_IMPORTED_MODULE_2__.VideoPresets.h720.resolution\n                }\n            });\n            setRoom(livekitRoom);\n            // Handle room events\n            livekitRoom.on(livekit_client__WEBPACK_IMPORTED_MODULE_2__.RoomEvent.DataReceived, (message)=>{\n                const data = new TextDecoder().decode(message);\n                console.log(\"Room message:\", JSON.parse(data));\n            });\n            // Handle media streams\n            const newMediaStream = new MediaStream();\n            setMediaStream(newMediaStream);\n            livekitRoom.on(livekit_client__WEBPACK_IMPORTED_MODULE_2__.RoomEvent.TrackSubscribed, (track)=>{\n                if (track.kind === \"video\" || track.kind === \"audio\") {\n                    newMediaStream.addTrack(track.mediaStreamTrack);\n                    if (newMediaStream.getVideoTracks().length > 0 && newMediaStream.getAudioTracks().length > 0) {\n                        if (mediaElementRef.current) {\n                            mediaElementRef.current.srcObject = newMediaStream;\n                            updateStatus(\"Media stream ready - Avatar is live!\", \"success\");\n                        }\n                    }\n                }\n            });\n            // Handle media stream removal\n            livekitRoom.on(livekit_client__WEBPACK_IMPORTED_MODULE_2__.RoomEvent.TrackUnsubscribed, (track)=>{\n                const mediaTrack = track.mediaStreamTrack;\n                if (mediaTrack) {\n                    newMediaStream.removeTrack(mediaTrack);\n                }\n            });\n            // Handle room connection state changes\n            livekitRoom.on(livekit_client__WEBPACK_IMPORTED_MODULE_2__.RoomEvent.Disconnected, (reason)=>{\n                updateStatus(\"Room disconnected: \".concat(reason), \"warning\");\n            });\n            await livekitRoom.prepareConnection(data.sessionData.url, data.sessionData.access_token);\n            updateStatus(\"Connection prepared successfully\", \"success\");\n            await connectWebSocket(data.sessionData.session_id, token);\n            updateStatus(\"Session created successfully\", \"success\");\n            // Return both session data, token, and room for immediate use\n            return {\n                sessionData: data.sessionData,\n                token,\n                room: livekitRoom\n            };\n        } catch (error) {\n            updateStatus(\"Failed to create session: \".concat(error.message), \"error\");\n            throw error;\n        }\n    };\n    // Start streaming session\n    const startStreamingSession = async (sessionData, token, livekitRoom)=>{\n        if (!sessionData || !sessionData.session_id) {\n            throw new Error('No session info available');\n        }\n        try {\n            updateStatus(\"Debug: Using token: \".concat(token ? 'Token exists' : 'No token'), \"info\");\n            updateStatus(\"Debug: Session ID: \".concat(sessionData.session_id), \"info\");\n            const startResponse = await fetch(\"\".concat(BACKEND_URL, \"/api/heygen/session/start\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    token: token,\n                    sessionId: sessionData.session_id\n                })\n            });\n            updateStatus(\"Debug: Start response status: \".concat(startResponse.status), \"info\");\n            if (!startResponse.ok) {\n                const errorText = await startResponse.text();\n                updateStatus(\"Debug: Error response: \".concat(errorText), \"error\");\n                throw new Error(\"HTTP error! status: \".concat(startResponse.status, \" - \").concat(errorText));\n            }\n            // Connect to LiveKit room\n            updateStatus(\"Debug: Available session properties: \".concat(Object.keys(sessionData).join(', ')), \"info\");\n            updateStatus(\"Debug: Room exists: \".concat(!!livekitRoom), \"info\");\n            updateStatus(\"Debug: URL exists: \".concat(!!sessionData.url), \"info\");\n            updateStatus(\"Debug: Access token exists: \".concat(!!sessionData.access_token), \"info\");\n            if (livekitRoom && sessionData.url && sessionData.access_token) {\n                await livekitRoom.connect(sessionData.url, sessionData.access_token);\n                updateStatus(\"Connected to room successfully\", \"success\");\n            } else {\n                updateStatus(\"Warning: Room or connection details missing\", \"warning\");\n                updateStatus(\"Debug: Missing - Room: \".concat(!livekitRoom, \", URL: \").concat(!sessionData.url, \", Token: \").concat(!sessionData.access_token), \"error\");\n            }\n            setIsStarted(true);\n            updateStatus(\"Streaming started successfully\", \"success\");\n        } catch (error) {\n            updateStatus(\"Failed to start streaming: \".concat(error.message), \"error\");\n            throw error;\n        }\n    };\n    // Send text to avatar\n    const sendText = async function(text) {\n        let taskType = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"talk\";\n        if (!sessionInfo) {\n            updateStatus(\"No active session - please start the avatar first\", \"warning\");\n            return;\n        }\n        const token = sessionToken;\n        if (!token) {\n            updateStatus(\"No session token available\", \"error\");\n            return;\n        }\n        try {\n            updateStatus(\"Sending message to avatar...\", \"info\");\n            const response = await fetch(\"\".concat(API_CONFIG.serverUrl, \"/v1/streaming.task\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    session_id: sessionInfo.session_id,\n                    text: text,\n                    task_type: taskType\n                })\n            });\n            const data = await response.json();\n            if (!response.ok || data.error) {\n                updateStatus(\"HeyGen API error: \".concat(data.error || response.statusText), \"error\");\n            } else {\n                updateStatus(\"✓ Message sent successfully (\".concat(taskType, \")\"), \"success\");\n                updateStatus('Avatar will speak: \"'.concat(text.substring(0, 50)).concat(text.length > 50 ? '...' : '', '\"'), \"info\");\n            }\n        } catch (err) {\n            updateStatus(\"HeyGen API call failed: \" + err.message, \"error\");\n        }\n    };\n    // Close session\n    const closeSession = async ()=>{\n        if (!sessionInfo) {\n            updateStatus(\"No active session\", \"warning\");\n            return;\n        }\n        try {\n            const token = sessionToken;\n            if (token) {\n                const response = await fetch(\"\".concat(API_CONFIG.serverUrl, \"/v1/streaming.stop\"), {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        Authorization: \"Bearer \".concat(token)\n                    },\n                    body: JSON.stringify({\n                        session_id: sessionInfo.session_id\n                    })\n                });\n            }\n            // Close WebSocket\n            if (webSocket) {\n                webSocket.close();\n            }\n            // Disconnect from LiveKit room\n            if (room) {\n                room.disconnect();\n            }\n            // Reset states\n            if (mediaElementRef.current) {\n                mediaElementRef.current.srcObject = null;\n            }\n            setSessionInfo(null);\n            setRoom(null);\n            setMediaStream(null);\n            setSessionToken(null);\n            setIsStarted(false);\n            updateStatus(\"Session closed successfully\", \"warning\");\n        } catch (error) {\n            updateStatus(\"Error closing session: \".concat(error.message), \"error\");\n            // Still reset states even if API call fails\n            setSessionInfo(null);\n            setRoom(null);\n            setMediaStream(null);\n            setSessionToken(null);\n            setIsStarted(false);\n        }\n    };\n    // Azure Search and OpenAI integration\n    const callAzureOpenAI = async (prompt)=>{\n        try {\n            var _data_choices__message, _data_choices_, _data_choices;\n            const response = await fetch(\"http://172.24.175.70:5001\" + \"/api/openai\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    prompt\n                })\n            });\n            const data = await response.json();\n            if (!response.ok || !data.choices) {\n                const errorMsg = data.error ? typeof data.error === 'object' ? JSON.stringify(data.error) : data.error : response.statusText;\n                updateStatus(\"Azure OpenAI error: \" + errorMsg, \"error\");\n                return null;\n            }\n            return (_data_choices = data.choices) === null || _data_choices === void 0 ? void 0 : (_data_choices_ = _data_choices[0]) === null || _data_choices_ === void 0 ? void 0 : (_data_choices__message = _data_choices_.message) === null || _data_choices__message === void 0 ? void 0 : _data_choices__message.content;\n        } catch (err) {\n            updateStatus(\"Azure OpenAI call failed: \" + err.message, \"error\");\n            return null;\n        }\n    };\n    const searchAzure = async (query)=>{\n        try {\n            const response = await fetch(\"http://172.24.175.70:5001\" + \"/api/search\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    query,\n                    courseId\n                })\n            });\n            const data = await response.json();\n            if (!response.ok || !data.value) {\n                const errorMsg = data.error ? typeof data.error === 'object' ? JSON.stringify(data.error) : data.error : response.statusText;\n                updateStatus(\"Azure Search error: \" + errorMsg, \"error\");\n                return null;\n            }\n            updateStatus(\"Found \".concat(data.value.length, \" relevant documents\"), \"success\");\n            return (data.value || []).map((r)=>r.content).join('\\n\\n');\n        } catch (err) {\n            updateStatus(\"Azure Search call failed: \" + err.message, \"error\");\n            return null;\n        }\n    };\n    // Handle Ask AI button\n    const handleAskAI = async ()=>{\n        const text = taskInput.trim();\n        if (!text) {\n            updateStatus(\"Please enter a question first\", \"warning\");\n            return;\n        }\n        if (!sessionInfo) {\n            updateStatus(\"Please start the avatar session first\", \"warning\");\n            return;\n        }\n        try {\n            updateStatus('\\uD83D\\uDD0D Searching for: \"'.concat(text, '\"'), \"info\");\n            const searchResults = await searchAzure(text);\n            if (!searchResults) {\n                updateStatus(\"No relevant documents found for your question\", \"warning\");\n                return;\n            }\n            updateStatus(\"🤖 Processing with AI...\", \"info\");\n            const llmAnswer = await callAzureOpenAI('Based on the following context, answer the question: \"'.concat(text, '\"\\n\\nContext: ').concat(searchResults));\n            if (llmAnswer) {\n                updateStatus(\"\\uD83D\\uDCA1 AI Response: \".concat(llmAnswer.substring(0, 100)).concat(llmAnswer.length > 100 ? '...' : ''), \"success\");\n                // Clean the response for avatar speech\n                let cleaned = llmAnswer.replace(/[#*_`>-]+/g, '').replace(/\\n{2,}/g, ' ').replace(/\\s{2,}/g, ' ').trim();\n                updateStatus(\"\\uD83C\\uDFA4 Avatar speaking response...\", \"info\");\n                await sendText(cleaned, \"repeat\");\n                setTaskInput(\"\");\n            } else {\n                updateStatus(\"Failed to generate AI response\", \"error\");\n            }\n        } catch (error) {\n            updateStatus(\"Error processing question: \".concat(error.message), \"error\");\n        }\n    };\n    // Handle Voice Input button\n    const handleVoiceInput = ()=>{\n        if (!isVoiceSupported) {\n            updateStatus(\"Voice input not supported in this browser\", \"warning\");\n            return;\n        }\n        if (!recognition) {\n            updateStatus(\"Voice recognition not initialized\", \"error\");\n            return;\n        }\n        if (isRecording) {\n            // Stop recording\n            recognition.stop();\n            setIsRecording(false);\n            updateStatus(\"Voice recording stopped\", \"info\");\n        } else {\n            // Start recording\n            try {\n                recognition.start();\n                setIsRecording(true);\n                updateStatus(\"🎤 Listening... Speak your question\", \"info\");\n            } catch (error) {\n                updateStatus(\"Failed to start voice recording: \".concat(error.message), \"error\");\n                setIsRecording(false);\n            }\n        }\n    };\n    // Handle Start button\n    const handleStart = async ()=>{\n        try {\n            const result = await createNewSession();\n            await startStreamingSession(result.sessionData, result.token, result.room);\n        } catch (error) {\n            updateStatus(\"Error starting session: \".concat(error.message), \"error\");\n        }\n    };\n    // Handle Enter key in textarea\n    const handleKeyDown = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleAskAI();\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-2xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-white bg-opacity-20 rounded-xl flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-white\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 597,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                            lineNumber: 596,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                        lineNumber: 595,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-bold\",\n                                                children: \"AI Avatar Assistant\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 601,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white text-opacity-70 text-sm\",\n                                                children: \"Powered by EXL\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 602,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                        lineNumber: 600,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                lineNumber: 594,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3 h-3 rounded-full \".concat(isStarted ? 'bg-green-400 animate-pulse' : 'bg-gray-400')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 607,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-opacity-80 text-sm font-medium\",\n                                                children: isStarted ? 'Live' : 'Offline'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 608,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                        lineNumber: 606,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: onClose,\n                                        className: \"text-white hover:text-gray-200 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M6 18L18 6M6 6l12 12\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 617,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                            lineNumber: 616,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                        lineNumber: 612,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                lineNumber: 605,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                        lineNumber: 593,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                    lineNumber: 592,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 max-h-[calc(90vh-120px)] overflow-y-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-5 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 rounded-2xl p-6 border border-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-800 mb-4 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5 mr-2 text-cyan-600\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                        lineNumber: 636,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                    lineNumber: 635,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Ask Your Question\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                            lineNumber: 634,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        placeholder: \"Type your question here...\",\n                                                        value: taskInput,\n                                                        onChange: (e)=>setTaskInput(e.target.value),\n                                                        onKeyDown: handleKeyDown,\n                                                        className: \"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all duration-200 resize-none h-24\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                        lineNumber: 643,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                    lineNumber: 642,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleAskAI,\n                                                            className: \"px-4 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4 inline mr-2\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                        lineNumber: 658,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                    lineNumber: 657,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                \"Ask AI\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                            lineNumber: 653,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleVoiceInput,\n                                                            disabled: !isVoiceSupported,\n                                                            className: \"px-4 py-3 text-white rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg \".concat(isRecording ? 'bg-red-500 hover:bg-red-600 animate-pulse' : isVoiceSupported ? 'bg-purple-500 hover:bg-purple-600' : 'bg-gray-400 cursor-not-allowed'),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4 inline mr-2\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: isRecording ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a2 2 0 114 0v4a2 2 0 11-4 0V7z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                        lineNumber: 675,\n                                                                        columnNumber: 27\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                        lineNumber: 677,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                    lineNumber: 673,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                isRecording ? 'Stop Recording' : 'Voice Input'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                            lineNumber: 662,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                    lineNumber: 652,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                            lineNumber: 641,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                    lineNumber: 633,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                lineNumber: 629,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 rounded-2xl p-6 border border-gray-200 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-lg font-semibold text-gray-800 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5 mr-2 text-blue-600\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M2 6a2 2 0 012-2h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 694,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                            lineNumber: 693,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \"AI Avatar\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                    lineNumber: 692,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 rounded-full \".concat(isAutoStarting ? 'bg-yellow-500 animate-pulse' : isStarted ? 'bg-green-500 animate-pulse' : 'bg-gray-400')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                            lineNumber: 699,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: isAutoStarting ? 'Starting...' : isStarted ? 'Connected' : 'Ready'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                            lineNumber: 706,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                    lineNumber: 698,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                            lineNumber: 691,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                ref: mediaElementRef,\n                                                className: \"w-full h-96 bg-gray-900 rounded-xl object-cover shadow-lg\",\n                                                autoPlay: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center text-white\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-16 h-16 mx-auto mb-4 opacity-50\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                    lineNumber: 721,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 720,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-lg font-medium\",\n                                                                children: \"Avatar will appear here\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 723,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm opacity-75\",\n                                                                children: \"Auto-starting avatar session...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 724,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                        lineNumber: 719,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                    lineNumber: 718,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 713,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                            lineNumber: 712,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                    lineNumber: 690,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                lineNumber: 688,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                        lineNumber: 626,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                    lineNumber: 625,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n            lineNumber: 590,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n        lineNumber: 589,\n        columnNumber: 5\n    }, undefined);\n};\n_s(QnAWithHeyGenModal, \"KD4zp0K+Vr4yX6MzZrH14V/PMpU=\");\n_c = QnAWithHeyGenModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (QnAWithHeyGenModal);\nvar _c;\n$RefreshReg$(_c, \"QnAWithHeyGenModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/QnAWithHeyGenModal.tsx\n"));

/***/ })

});