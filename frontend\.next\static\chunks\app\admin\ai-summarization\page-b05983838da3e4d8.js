(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[871],{285:(e,t,a)=>{"use strict";a.d(t,{$:()=>i});var r=a(5155);a(2115);var s=a(9708),n=a(2085),l=a(9434);let o=(0,n.F)("cursor-pointer inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"underline-offset-4 hover:underline text-primary",brand:"bg-brand-blue text-white border border-brand-blue hover:bg-brand-blue/90 hover:border-brand-blue/80",sucess:"bg-green-600 text-white border border-green-700 hover:bg-green-600/90 hover:border-green-600/80",warning:"bg-amber-400 text-gray-900 border border-yellow-500 hover:bg-yellow-400/90 hover:border-yellow-400/80"},size:{default:"h-10 py-2 px-4",sm:"h-9 px-3 rounded-md",lg:"h-11 px-8 rounded-md",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}});function i(e){let{className:t,variant:a,size:n,asChild:i=!1,...d}=e,c=i?s.DX:"button";return(0,r.jsx)(c,{"data-slot":"button",className:(0,l.cn)(o({variant:a,size:n,className:t})),...d})}},5057:(e,t,a)=>{"use strict";a.d(t,{J:()=>l});var r=a(5155);a(2115);var s=a(968),n=a(9434);function l(e){let{className:t,...a}=e;return(0,r.jsx)(s.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}},5763:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>v});var r=a(5155),s=a(285),n=a(5057),l=a(9409),o=a(2115),i=a(9613),d=a(9434);function c(e){let{delayDuration:t=0,...a}=e;return(0,r.jsx)(i.Kq,{"data-slot":"tooltip-provider",delayDuration:t,...a})}function u(e){let{...t}=e;return(0,r.jsx)(c,{children:(0,r.jsx)(i.bL,{"data-slot":"tooltip",...t})})}function x(e){let{...t}=e;return(0,r.jsx)(i.l9,{"data-slot":"tooltip-trigger",...t})}function m(e){let{className:t,sideOffset:a=0,variant:s="default",children:n,...l}=e,o="";switch(s){case"info":o="bg-blue-600 text-white";break;case"success":o="bg-green-600 text-white";break;case"danger":o="bg-red-600 text-white";break;case"warning":o="bg-yellow-400 text-gray-900";break;case"brand":o="bg-brand-blue text-white border border-brand-blue";break;default:o="bg-primary text-primary-foreground"}return(0,r.jsx)(i.ZL,{children:(0,r.jsxs)(i.UC,{"data-slot":"tooltip-content",sideOffset:a,className:(0,d.cn)("animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-[--radix-tooltip-content-transform-origin] rounded-md px-3 py-1.5 text-xs text-balance",o,t),...l,children:[n,(0,r.jsx)(i.i3,{className:(0,d.cn)("z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]",o)})]})})}var g=a(5196),p=a(437);function h(e){let{pages:t,selected:a,onSelect:s}=e;return(0,r.jsxs)("div",{className:"w-full mb-8",children:[(0,r.jsxs)("h3",{className:" font-semibold text-brand-blue/[85%] mt-2 mb-4",children:["Medical Course Pages (",t.length," pages)"]}),(0,r.jsx)("ul",{className:"h-full space-y-3 overflow-y-auto max-h-[calc(100vh-5%)] pr-2 custom-scrollbar",children:t.map((e,t)=>(0,r.jsx)("li",{children:(0,r.jsxs)("button",{className:"w-full text-left px-5 py-3 rounded-xl border transition-all duration-200 flex flex-col gap-1 shadow-sm \n                ".concat(a===t?"border-orange-500 bg-orange-50 text-orange-800 shadow-md":"border-gray-200 bg-white hover:bg-orange-50 hover:border-orange-300","\n              "),onClick:()=>s(t),children:[(0,r.jsxs)("div",{className:"font-bold text-lg",children:["Page ",e.page]}),(0,r.jsx)("div",{className:"text-sm text-gray-600 truncate",children:e.title}),(0,r.jsx)("span",{className:"inline-block mt-1 text-xs px-3 py-1 rounded-full font-medium \n                ".concat("Approved"===e.status?"bg-green-100 text-green-700":"bg-orange-100 text-orange-600","\n              "),children:e.status})]})},e.page))})]})}function b(e){let{page:t,onChangeSummary:a,onApprove:n,onRegenerate:l,onCustomPrompt:o,onViewOriginal:i,onApproveAll:d,approvedCount:c,totalCount:b,pages:f,selected:v,onSelect:y}=e,j=b>0?c/b*100:0;return(0,r.jsxs)("div",{className:"flex-1 flex flex-col bg-white rounded-2xl shadow-xl px-4 py-4 border border-orange-100",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("h2",{className:"text-2xl font-bold text-brand-blue flex items-center gap-2",children:[(0,r.jsx)("svg",{className:"w-7 h-7 text-orange-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"})}),(0,r.jsx)("span",{className:"text-brand-blue",children:"AI-Powered Medical Summarization"})]}),(0,r.jsxs)("div",{className:"text-md font-medium text-brand-blue-300",children:["Progress: ",(0,r.jsx)("span",{className:"text-orange-600",children:c})," of ",(0,r.jsx)("span",{className:"text-brand-blue-300",children:b})," approved"]})]}),(0,r.jsx)("div",{className:"w-full bg-brand-blue-300/[20%] rounded-full h-2.5 mb-6",children:(0,r.jsx)("div",{className:"bg-green-500 h-2.5 rounded-full transition-all duration-500 ease-out",style:{width:"".concat(j,"%")}})}),(0,r.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,r.jsx)("div",{className:"col-span-2",children:(0,r.jsxs)("div",{className:"flex-1 flex flex-col",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4 mb-4",children:[(0,r.jsx)("h3",{className:"font-bold text-brand-blue-300",children:t.title}),(0,r.jsxs)("div",{className:"flex-1 flex justify-end gap-2",children:[(0,r.jsxs)(u,{children:[(0,r.jsx)(x,{asChild:!0,children:(0,r.jsx)(s.$,{variant:"outline",className:"px-4 py-2 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-100 text-sm font-medium transition flex items-center gap-1 shadow-sm",onClick:l,children:(0,r.jsxs)("svg",{className:"w-4 h-4 text-gray-600",fill:"none",stroke:"currentColor",strokeWidth:"2",viewBox:"0 0 24 24",children:[(0,r.jsx)("path",{d:"M4 4v5h.582M20 20v-5h-.581"}),(0,r.jsx)("path",{d:"M5.077 19A9 9 0 1 0 6.5 6.5L4 9.5"})]})})}),(0,r.jsx)(m,{variant:"brand",children:(0,r.jsx)("p",{children:"Regenerate"})})]}),(0,r.jsxs)(u,{children:[(0,r.jsx)(x,{asChild:!0,children:(0,r.jsx)(s.$,{variant:"outline",className:"px-4 py-2 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-100 text-sm font-medium transition flex items-center gap-1 shadow-sm",onClick:o,children:(0,r.jsxs)("svg",{className:"w-4 h-4 text-gray-600",fill:"none",stroke:"currentColor",strokeWidth:"2",viewBox:"0 0 24 24",children:[(0,r.jsx)("path",{d:"M16.862 3.487a2.6 2.6 0 1 1 3.677 3.677L7.5 20.205l-4.5 1 1-4.5L16.862 3.487Z"}),(0,r.jsx)("path",{d:"M15 6.5 17.5 9"})]})})}),(0,r.jsx)(m,{variant:"brand",children:(0,r.jsx)("p",{children:"Custom Prompt"})})]}),(0,r.jsxs)(u,{children:[(0,r.jsx)(x,{asChild:!0,children:(0,r.jsx)(s.$,{variant:"outline",className:"px-4 py-2 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-100 text-sm font-medium transition flex items-center gap-1 shadow-sm",onClick:i,children:(0,r.jsxs)("svg",{className:"w-4 h-4 text-gray-600",fill:"none",stroke:"currentColor",strokeWidth:"2",viewBox:"0 0 24 24",children:[(0,r.jsx)("path",{d:"M1.5 12S5 5 12 5s10.5 7 10.5 7-3.5 7-10.5 7S1.5 12 1.5 12Z"}),(0,r.jsx)("circle",{cx:"12",cy:"12",r:"3"})]})})}),(0,r.jsx)(m,{variant:"brand",children:(0,r.jsx)("p",{children:"View Original"})})]})]})]}),(0,r.jsxs)("div",{className:"mb-6 flex-1 flex flex-col",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"AI-Generated Summary"}),(0,r.jsx)("textarea",{className:"w-full border border-gray-200 rounded-lg px-4 py-3 min-h-[150px] focus:ring-2 focus:ring-orange-200 focus:border-orange-300 transition-colors duration-200 resize-y flex-1",value:t.aiSummary,onChange:e=>a(e.target.value)}),(0,r.jsxs)("div",{className:"flex items-center gap-2 mt-4 text-xs text-gray-500",children:[(0,r.jsx)("span",{className:"bg-orange-50 text-orange-600 px-2 py-1 rounded-full font-medium",children:"Generated by Azure OpenAI GPT-4"}),(0,r.jsx)("span",{className:"bg-brand-blue/[25%] text-brand-blue px-2 py-1 rounded-full font-medium",children:"Medical AI Model"})]})]}),(0,r.jsxs)("div",{className:"mb-6 flex-1 flex flex-col",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Original Content Preview"}),(0,r.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 text-gray-700 text-sm min-h-[150px] overflow-y-auto flex-1 custom-scrollbar",children:t.original})]}),(0,r.jsxs)("div",{className:"flex gap-3 mt-2 justify-start",children:[(0,r.jsxs)(s.$,{variant:"outline",className:"text-brand-blue border-brand-blue hover:bg-brand-blue hover:text-white px-6 py-3 rounded-lg font-semibold shadow-md hover:shadow-lg transition-all duration-200 flex items-center gap-2",onClick:n,children:[(0,r.jsx)(g.A,{className:"w-5 h-5"}),"Approve"]}),(0,r.jsxs)(s.$,{variant:"outline",className:"text-brand-blue border-brand-blue hover:bg-brand-blue hover:text-white px-6 py-3 rounded-lg font-semibold shadow-md hover:shadow-lg transition-all duration-200 flex items-center gap-2",onClick:d,children:[(0,r.jsx)(p.A,{className:"w-5 h-5"}),"Approve All (",f.filter(e=>"Pending Review"===e.status).length," remaining)"]})]})]})}),(0,r.jsx)("div",{className:"",children:(0,r.jsx)(h,{pages:f,selected:v,onSelect:y})})]})]})}function f(){let[e,t]=(0,o.useState)(0),[a,s]=(0,o.useState)([]),[i,d]=(0,o.useState)(!1),[c,u]=(0,o.useState)(""),[x,m]=(0,o.useState)([]),[g,p]=(0,o.useState)(""),[h,f]=(0,o.useState)(!1),[v,y]=(0,o.useState)(!1),[j,w]=(0,o.useState)(""),[N,k]=(0,o.useState)(null);(0,o.useEffect)(()=>{fetch("http://172.24.175.70:5001/api/courses").then(e=>e.json()).then(e=>m(e))},[]);let C=async e=>{p(e),f(!0);let a=await fetch("http://172.24.175.70:5001/api/courses/"+e),r=await a.json();s((r.pdf_output||[]).map((e,t)=>({page:e.page||t+1,title:"Page ".concat(e.page||t+1),status:"Pending Review",aiSummary:e.imagetext||"",original:e.imagetext||"",imageassestid:e.imageassestid}))),t(0),f(!1),k(r.blob_url||null)},S=()=>{s(e=>e.map(e=>({...e,status:"Approved"})))},z=async()=>{let t=a[e].aiSummary;try{let a=await fetch("".concat("http://172.24.175.70:5001","/api/rephrase"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({text:t})}),r=await a.json();r&&r.rephrased?s(t=>t.map((t,a)=>a===e?{...t,aiSummary:r.rephrased}:t)):"string"==typeof r&&s(t=>t.map((t,a)=>a===e?{...t,aiSummary:r}:t))}catch(e){alert("Failed to rephrase.")}},A=async()=>{let t=a[e].aiSummary;try{let a=await fetch("".concat("http://172.24.175.70:5001","/api/customrephrase"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({text:t,prompt:c})}),r=await a.json();r&&r.result?s(t=>t.map((t,a)=>a===e?{...t,aiSummary:r.result}:t)):alert("Failed to get rephrased result.")}catch(e){alert("Failed to get rephrased result.")}d(!1),u("")},_=async()=>{y(!1),S();let e="unknown";try{let t=localStorage.getItem("token");if(t){let a=atob(t.split(".")[1]);e=JSON.parse(a).email||"unknown"}}catch(e){}try{(await fetch("".concat("http://172.24.175.70:5001","/api/courses/").concat(g,"/approve"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({content:a.map(e=>({...e,status:"Approved"})),approved_by:e,comment:""})})).ok?(w("Content approved and saved!"),setTimeout(()=>w(""),3e3)):alert("Failed to save approved content!")}catch(e){alert("Failed to save approved content!")}};return(0,r.jsxs)("div",{className:"w-full px-4 py-4 flex flex-col items-stretch justify-start relative",children:[(0,r.jsx)("form",{className:"flex gap-4 items-end py-4",onSubmit:e=>e.preventDefault(),children:(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)(n.J,{htmlFor:"course",className:"py-2",children:"Course"}),(0,r.jsxs)(l.l6,{value:g||"",onValueChange:e=>C(e),children:[(0,r.jsx)(l.bq,{className:"w-auto focus:ring-2 focus:ring-orange-200",children:(0,r.jsx)(l.yv,{placeholder:"Select Course"})}),(0,r.jsx)(l.gC,{children:x.map(e=>(0,r.jsxs)(l.eb,{value:e.id.toString(),children:[e.title," (",e.domain,")"]},e.id))})]})]})}),h&&(0,r.jsx)("div",{className:"text-center text-lg text-orange-500",children:"Loading course data..."}),!h&&a.length>0&&(0,r.jsx)(b,{page:a[e],onChangeSummary:t=>{s(a=>a.map((a,r)=>r===e?{...a,aiSummary:t}:a))},onApprove:()=>{s(t=>t.map((t,a)=>a===e?{...t,status:"Approved"}:t))},onRegenerate:z,onCustomPrompt:()=>{d(!0)},onViewOriginal:()=>{N?window.open(N,"_blank"):alert("No original content URL available.")},onApproveAll:()=>{y(!0)},approvedCount:a.filter(e=>"Approved"===e.status).length,totalCount:a.length,pages:a,selected:e,onSelect:t}),i&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,r.jsxs)("div",{className:"bg-white rounded-2xl shadow-2xl p-8 max-w-md w-full animate-fade-in-up relative",children:[(0,r.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-4",children:"Custom AI Prompt"}),(0,r.jsx)("textarea",{className:"w-full border border-gray-300 rounded-lg p-3 min-h-[120px] focus:ring-2 focus:ring-orange-200 focus:border-orange-300 transition-colors duration-200 resize-y",placeholder:"Enter your custom prompt here to regenerate the summary...",value:c,onChange:e=>u(e.target.value)}),(0,r.jsxs)("div",{className:"flex justify-end gap-3 mt-6",children:[(0,r.jsx)("button",{className:"bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-3 rounded-lg font-semibold transition-colors duration-200",onClick:()=>{d(!1),u("")},children:"Cancel"}),(0,r.jsx)("button",{className:"bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-200",onClick:A,children:"Generate"})]})]})}),v&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,r.jsxs)("div",{className:"bg-white rounded-2xl shadow-2xl p-8 max-w-sm w-full animate-fade-in-up relative text-center",children:[(0,r.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-4",children:"Confirm Approval"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"Are you sure you want to approve all pending summaries for this course? This action cannot be undone."}),(0,r.jsxs)("div",{className:"flex justify-center gap-4",children:[(0,r.jsx)("button",{className:"bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-3 rounded-lg font-semibold transition-colors duration-200",onClick:()=>{y(!1)},children:"Cancel"}),(0,r.jsx)("button",{className:"bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-200",onClick:_,children:"Confirm Approve All"})]})]})}),j&&(0,r.jsx)("div",{className:"fixed bottom-6 right-6 bg-green-500 text-white px-6 py-3 rounded-lg shadow-xl text-lg font-semibold animate-fade-in-right z-50",children:j})]})}function v(){return(0,r.jsx)(f,{})}},7283:(e,t,a)=>{Promise.resolve().then(a.bind(a,5763))},9409:(e,t,a)=>{"use strict";a.d(t,{bq:()=>u,eb:()=>m,gC:()=>x,l6:()=>d,yv:()=>c});var r=a(5155);a(2115);var s=a(9963),n=a(6474),l=a(5196),o=a(7863),i=a(9434);function d(e){let{...t}=e;return(0,r.jsx)(s.bL,{"data-slot":"select",...t})}function c(e){let{...t}=e;return(0,r.jsx)(s.WT,{"data-slot":"select-value",...t})}function u(e){let{className:t,size:a="default",children:l,...o}=e;return(0,r.jsxs)(s.l9,{"data-slot":"select-trigger","data-size":a,className:(0,i.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...o,children:[l,(0,r.jsx)(s.In,{asChild:!0,children:(0,r.jsx)(n.A,{className:"size-4 opacity-50"})})]})}function x(e){let{className:t,children:a,position:n="popper",...l}=e;return(0,r.jsx)(s.ZL,{children:(0,r.jsxs)(s.UC,{"data-slot":"select-content",className:(0,i.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:n,...l,children:[(0,r.jsx)(g,{}),(0,r.jsx)(s.LM,{className:(0,i.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,r.jsx)(p,{})]})})}function m(e){let{className:t,children:a,...n}=e;return(0,r.jsxs)(s.q7,{"data-slot":"select-item",className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...n,children:[(0,r.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(s.VF,{children:(0,r.jsx)(l.A,{className:"size-4"})})}),(0,r.jsx)(s.p4,{children:a})]})}function g(e){let{className:t,...a}=e;return(0,r.jsx)(s.PP,{"data-slot":"select-scroll-up-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,r.jsx)(o.A,{className:"size-4"})})}function p(e){let{className:t,...a}=e;return(0,r.jsx)(s.wn,{"data-slot":"select-scroll-down-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,r.jsx)(n.A,{className:"size-4"})})}},9434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>n});var r=a(2596),s=a(9688);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.QP)((0,r.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[352,578,25,441,684,358],()=>t(7283)),_N_E=e.O()}]);