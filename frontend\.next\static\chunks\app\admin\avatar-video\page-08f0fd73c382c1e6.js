(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[736],{285:(e,t,s)=>{"use strict";s.d(t,{$:()=>o});var a=s(5155);s(2115);var r=s(9708),n=s(2085),l=s(9434);let i=(0,n.F)("cursor-pointer inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"underline-offset-4 hover:underline text-primary",brand:"bg-brand-blue text-white border border-brand-blue hover:bg-brand-blue/90 hover:border-brand-blue/80",sucess:"bg-green-600 text-white border border-green-700 hover:bg-green-600/90 hover:border-green-600/80",warning:"bg-amber-400 text-gray-900 border border-yellow-500 hover:bg-yellow-400/90 hover:border-yellow-400/80"},size:{default:"h-10 py-2 px-4",sm:"h-9 px-3 rounded-md",lg:"h-11 px-8 rounded-md",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:s,size:n,asChild:o=!1,...c}=e,d=o?r.DX:"button";return(0,a.jsx)(d,{"data-slot":"button",className:(0,l.cn)(i({variant:s,size:n,className:t})),...c})}},602:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>O});var a=s(5155),r=s(285),n=s(5057),l=s(9409),i=s(2115),o=s(9434);function c(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,o.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...s})}function d(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,o.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...s})}function x(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,o.cn)("leading-none font-semibold",t),...s})}function m(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,o.cn)("px-6",t),...s})}var u=s(9708);let h=(0,s(2085).F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function g(e){let{className:t,variant:s,asChild:r=!1,...n}=e,l=r?u.DX:"span";return(0,a.jsx)(l,{"data-slot":"badge",className:(0,o.cn)(h({variant:s}),t),...n})}var b=s(5863);function f(e){let{className:t,value:s,...r}=e;return(0,a.jsx)(b.bL,{"data-slot":"progress",className:(0,o.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",t),...r,children:(0,a.jsx)(b.C1,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:"translateX(-".concat(100-(s||0),"%)")}})})}var v=s(9870);function p(e){let{className:t,orientation:s="horizontal",decorative:r=!0,...n}=e;return(0,a.jsx)(v.b,{"data-slot":"separator",decorative:r,orientation:s,className:(0,o.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",t),...n})}var j=s(987),N=s(7580),y=s(2138),w=s(2659),A=s(5690),_=s(5041),k=s(646),C=s(3311),S=s(9803),z=s(4186),V=s(5452),G=s(4416);function F(e){let{...t}=e;return(0,a.jsx)(V.bL,{"data-slot":"dialog",...t})}function P(e){let{...t}=e;return(0,a.jsx)(V.ZL,{"data-slot":"dialog-portal",...t})}function D(e){let{className:t,...s}=e;return(0,a.jsx)(V.hJ,{"data-slot":"dialog-overlay",className:(0,o.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...s})}function L(e){let{className:t,children:s,showCloseButton:r=!0,...n}=e;return(0,a.jsxs)(P,{"data-slot":"dialog-portal",children:[(0,a.jsx)(D,{}),(0,a.jsxs)(V.UC,{"data-slot":"dialog-content",className:(0,o.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...n,children:[s,r&&(0,a.jsxs)(V.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,a.jsx)(G.A,{}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function E(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"dialog-header",className:(0,o.cn)("flex flex-col gap-2 text-center sm:text-left",t),...s})}function $(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"dialog-footer",className:(0,o.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...s})}function q(e){let{className:t,...s}=e;return(0,a.jsx)(V.hE,{"data-slot":"dialog-title",className:(0,o.cn)("text-lg leading-none font-semibold",t),...s})}function I(e){let{className:t,...s}=e;return(0,a.jsx)(V.VY,{"data-slot":"dialog-description",className:(0,o.cn)("text-muted-foreground text-sm",t),...s})}function J(){var e,t,s;let[o,u]=(0,i.useState)([]),[h,b]=(0,i.useState)(null),[v,V]=(0,i.useState)([]),[G,P]=(0,i.useState)(null),[D,J]=(0,i.useState)(null),[O,T]=(0,i.useState)(!0),[M,U]=(0,i.useState)(!0),[R,X]=(0,i.useState)(!1),[H,W]=(0,i.useState)([]),[Y,Z]=(0,i.useState)(!1),[B,Q]=(0,i.useState)({current:0,total:0}),[K,ee]=(0,i.useState)("");(0,i.useEffect)(()=>{let e=async()=>{try{let e=await fetch("http://*************:5001/api/avatars"),t=await e.json();Array.isArray(t)?u(t):(console.error("API returned non-array data for avatars:",t),u([]),alert("Failed to load avatar configurations: Invalid data format."))}catch(e){console.error("Failed to fetch avatars:",e),alert("Failed to load avatar configurations. Please ensure backend is running.")}finally{U(!1)}};(async()=>{try{let e=await fetch("http://*************:5001/api/all-approved-content-versions"),t=await e.json();V(t)}catch(e){console.error("Failed to fetch approved versions:",e),alert("Failed to load approved content versions.")}finally{T(!1)}})(),e()},[]);let et=v.reduce((e,t)=>(e.find(e=>e.course_id===t.course_id)||e.push({course_id:t.course_id,course_title:t.course_title}),e),[]),es=v.filter(e=>e.course_id===Number(G)),ea=async()=>{Z(!1),X(!0),W([]),Q({current:0,total:0}),ee("");try{let e=await fetch("".concat("http://*************:5001","/api/course-content-versions/").concat(D)),t=await e.json(),s=Array.isArray(t.content)?t.content.length:0;Q({current:0,total:s});let a=await fetch("http://*************:5001/api/generate-course-videos",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({selectedAvatarId:h,selectedVersionId:D})}),r=await a.json();if(!a.ok)throw Error(r.error||"Failed to initiate video generation");console.log("Generated Videos Data:",r.generated_videos),W(r.generated_videos||[])}catch(e){console.error("Video generation error:",e),alert("Video generation failed: ".concat(e.message||"Unknown error"))}finally{X(!1),ee("")}};return(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8 bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen",children:[(0,a.jsx)(c,{className:"mb-8 border-0 shadow-xl bg-white/80 backdrop-blur-sm",children:(0,a.jsxs)(m,{className:"p-8",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("div",{className:"w-20 h-20 bg-orange-400 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg",children:(0,a.jsx)(j.A,{className:"w-10 h-10 text-white"})}),(0,a.jsx)("h2",{className:"text-4xl font-bold mb-3 text-gray-900 bg-brand-blue bg-clip-text text-transparent",children:"Medical Course Avatar & Video Generation"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed",children:"Select a medical professional avatar and generate engaging videos from your approved summaries with cutting-edge AI technology"})]}),(0,a.jsx)(p,{className:"my-8"}),(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-6 mb-8",children:[(0,a.jsx)(c,{className:"p-4 transition-all duration-300 ".concat(h?"border-blue-500 bg-blue-50 shadow-lg scale-105":"border-gray-200 hover:shadow-md"),children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-12 h-12 rounded-xl flex items-center justify-center transition-colors ".concat(h?"bg-blue-500":"bg-gray-200"),children:(0,a.jsx)(N.A,{className:"w-6 h-6 ".concat(h?"text-white":"text-gray-400")})}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("div",{className:"font-semibold ".concat(h?"text-blue-600":"text-gray-500"),children:"Select Avatar"}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Choose your presenter"})]})]})}),(0,a.jsx)(y.A,{className:"w-6 h-6 text-gray-300"}),(0,a.jsx)(c,{className:"p-4 transition-all duration-300 ".concat(D?"border-green-500 bg-green-50 shadow-lg scale-105":"border-gray-200 hover:shadow-md"),children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-12 h-12 rounded-xl flex items-center justify-center transition-colors ".concat(D?"bg-green-500":"bg-gray-200"),children:(0,a.jsx)(w.A,{className:"w-6 h-6 ".concat(D?"text-white":"text-gray-400")})}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("div",{className:"font-semibold ".concat(D?"text-green-600":"text-gray-500"),children:"Select Content"}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Choose course material"})]})]})}),(0,a.jsx)(y.A,{className:"w-6 h-6 text-gray-300"}),(0,a.jsx)(c,{className:"p-4 transition-all duration-300 ".concat(R?"border-amber-500 bg-amber-50 shadow-lg scale-105":"border-gray-200 hover:shadow-md"),children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-12 h-12 rounded-xl flex items-center justify-center transition-colors ".concat(R?"bg-amber-500":"bg-gray-200"),children:(0,a.jsx)(A.A,{className:"w-6 h-6 ".concat(R?"text-white":"text-gray-400")})}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("div",{className:"font-semibold ".concat(R?"text-amber-600":"text-gray-500"),children:"Generate Videos"}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Create content"})]})]})})]})]})}),(0,a.jsxs)(c,{className:"mb-8 border-0 shadow-xl",children:[(0,a.jsx)(d,{className:"pb-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center",children:(0,a.jsx)(N.A,{className:"w-7 h-7 text-blue-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)(x,{className:"text-2xl text-gray-900",children:"Select Medical Professional Avatar"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"Choose your video presenter from our collection of medical professionals"})]})]})}),(0,a.jsx)(m,{children:M?(0,a.jsx)("div",{className:"flex items-center justify-center py-16",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3 text-gray-500",children:[(0,a.jsx)("div",{className:"w-8 h-8 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin"}),(0,a.jsx)("span",{className:"text-lg",children:"Loading avatars..."})]})}):0===o.length?(0,a.jsxs)(c,{className:"p-12 text-center bg-gray-50",children:[(0,a.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDC65"}),(0,a.jsx)("div",{className:"text-gray-500 text-lg",children:"No avatars available."})]}):(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:o.map(e=>(0,a.jsx)(c,{className:"group relative cursor-pointer transition-all duration-300 hover:shadow-xl ".concat(h===e.id?"border-2 border-blue-500 bg-blue-50 shadow-xl scale-105":"border hover:border-blue-300 hover:bg-blue-50/50 hover:scale-102"),onClick:()=>b(e.id),children:(0,a.jsxs)(m,{className:"p-6 text-center",style:{minHeight:280},children:[(0,a.jsx)("div",{className:"w-16 h-16 rounded-full mx-auto mb-4 flex items-center justify-center transition-all ".concat(h===e.id?"bg-blue-500 shadow-lg":"bg-orange-100 group-hover:bg-blue-100"),children:(0,a.jsx)(_.A,{className:"w-8 h-8 transition-colors ".concat(h===e.id?"text-white":"text-orange-600 group-hover:text-blue-600")})}),(0,a.jsx)("h3",{className:"font-bold text-gray-900 mb-3 text-lg truncate",title:e.avatar_name,children:e.avatar_name}),(0,a.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,a.jsx)(g,{variant:"secondary",className:"text-xs",children:e.specialty||"N/A"}),(0,a.jsx)("div",{className:"text-xs text-gray-500 truncate",title:e.domain||"N/A",children:e.domain||"N/A"}),(0,a.jsxs)("div",{className:"text-xs text-orange-600 truncate",title:e.voice_id||"N/A",children:["Voice: ",e.voice_id||"N/A"]})]}),(0,a.jsx)(r.$,{variant:h===e.id?"default":"outline",className:"w-full transition-all duration-200 ".concat(h===e.id?"bg-blue-600 hover:bg-blue-700 text-white shadow-md":"hover:bg-blue-50 hover:text-blue-600 hover:border-blue-300"),children:h===e.id?(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(k.A,{className:"w-4 h-4 mr-2"}),"Selected"]}):"Select Avatar"})]})},e.id))})})]}),null!==h&&(0,a.jsxs)(c,{className:"mb-8 border-0 shadow-xl",children:[(0,a.jsx)(d,{className:"pb-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center",children:(0,a.jsx)(w.A,{className:"w-7 h-7 text-green-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)(x,{className:"text-2xl text-gray-900",children:"Select Course Content for Video Generation"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"Choose your course and approved version for video creation"})]})]})}),(0,a.jsx)(m,{children:O?(0,a.jsx)("div",{className:"flex items-center justify-center py-16",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3 text-gray-500",children:[(0,a.jsx)("div",{className:"w-8 h-8 border-2 border-gray-300 border-t-green-500 rounded-full animate-spin"}),(0,a.jsx)("span",{className:"text-lg",children:"Loading approved content versions..."})]})}):0===v.length?(0,a.jsxs)(c,{className:"p-12 text-center bg-gray-50",children:[(0,a.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDCDA"}),(0,a.jsx)("div",{className:"text-gray-500 text-lg",children:"No approved content versions available."})]}):(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(n.J,{htmlFor:"course",className:"text-base font-semibold text-gray-700",children:"Course Selection"}),(0,a.jsxs)(l.l6,{value:G||"",onValueChange:e=>{P(e),J(null)},children:[(0,a.jsx)(l.bq,{className:"w-full h-14 text-base focus:ring-2 focus:ring-green-200 border-2 rounded-xl",children:(0,a.jsx)(l.yv,{placeholder:"Choose a course to generate videos from"})}),(0,a.jsx)(l.gC,{children:et.map(e=>(0,a.jsx)(l.eb,{value:e.course_id.toString(),className:"text-base py-3",children:e.course_title},e.course_id))})]})]}),G&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(n.J,{htmlFor:"version",className:"text-base font-semibold text-gray-700",children:"Version Selection"}),(0,a.jsxs)(l.l6,{value:D||"",onValueChange:e=>{J(e)},children:[(0,a.jsx)(l.bq,{className:"w-full h-14 text-base focus:ring-2 focus:ring-green-200 border-2 rounded-xl",children:(0,a.jsx)(l.yv,{placeholder:"Select an approved version"})}),(0,a.jsx)(l.gC,{children:es.map(e=>(0,a.jsxs)(l.eb,{value:e.version_id.toString(),className:"text-base py-3",children:["Version ",e.version_number," (Approved: ",new Date(e.approved_at).toLocaleDateString(),")"]},e.version_id))})]})]})]})})]}),(0,a.jsxs)(c,{className:"border-0 shadow-xl",children:[(0,a.jsx)(d,{className:"pb-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-amber-100 rounded-xl flex items-center justify-center",children:(0,a.jsx)(j.A,{className:"w-7 h-7 text-amber-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)(x,{className:"text-2xl text-gray-900",children:"Video Generation Progress"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"Generate professional video content from your selections"})]})]}),(0,a.jsx)(r.$,{variant:"warning",size:"lg",className:"flex items-center space-x-3 px-8 py-4 text-white font-semibold rounded-2xl shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105",onClick:()=>{if(!h||!D)return void alert("Please select both an avatar and a content version.");Z(!0)},disabled:!h||!D||R,children:R?(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("svg",{className:"animate-spin w-6 h-6 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,a.jsx)("span",{className:"text-lg",children:"Generating Videos..."})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(j.A,{className:"w-6 h-6"}),(0,a.jsx)("span",{className:"text-lg",children:"Generate All Videos"})]})})]})}),(0,a.jsxs)(m,{children:[R&&B.total>0&&(0,a.jsx)(c,{className:"mb-8 bg-gradient-to-r from-blue-50 to-indigo-50 border-2 border-blue-200",children:(0,a.jsxs)(m,{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("h4",{className:"font-bold text-blue-900 text-lg flex items-center",children:[(0,a.jsx)(C.A,{className:"w-5 h-5 mr-2"}),"Generation Progress"]}),(0,a.jsxs)(g,{variant:"secondary",className:"bg-blue-100 text-blue-800 font-semibold",children:[B.current," of ",B.total," completed"]})]}),(0,a.jsx)(f,{value:B.current/B.total*100,className:"h-3 mb-4"}),K&&(0,a.jsxs)("p",{className:"text-sm text-blue-700 flex items-center",children:[(0,a.jsx)(S.A,{className:"w-4 h-4 mr-2"}),"Currently generating: ",(0,a.jsx)("span",{className:"font-medium ml-1",children:K})]})]})}),0===H.length&&!R&&(0,a.jsxs)("div",{className:"text-center py-20",children:[(0,a.jsx)("div",{className:"w-32 h-32 bg-gradient-to-br from-gray-100 to-gray-200 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-lg",children:(0,a.jsx)(j.A,{className:"w-16 h-16 text-gray-400"})}),(0,a.jsx)("h3",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Ready to Generate Videos"}),(0,a.jsx)("p",{className:"text-gray-600 max-w-lg mx-auto mb-8 text-lg leading-relaxed",children:'Select an avatar and course content, then click "Generate All Videos" to begin creating your professional video content with AI.'}),(!h||!D)&&(0,a.jsx)(c,{className:"bg-gradient-to-r from-amber-50 to-orange-50 border-2 border-amber-200 max-w-md mx-auto",children:(0,a.jsxs)(m,{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2 text-amber-700 mb-3",children:[(0,a.jsx)(z.A,{className:"w-6 h-6"}),(0,a.jsx)("span",{className:"font-semibold text-lg",children:"Selection Required"})]}),(0,a.jsx)("p",{className:"text-amber-600",children:"Please select both an avatar and course content to proceed with video generation."})]})}),h&&D&&(0,a.jsx)(c,{className:"bg-gradient-to-r from-green-50 to-emerald-50 border-2 border-green-200 max-w-md mx-auto",children:(0,a.jsxs)(m,{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2 text-green-700 mb-3",children:[(0,a.jsx)(k.A,{className:"w-6 h-6"}),(0,a.jsx)("span",{className:"font-semibold text-lg",children:"Ready to Generate!"})]}),(0,a.jsx)("p",{className:"text-green-600",children:"Avatar and content selected. Click the button above to start video generation."})]})})]}),H.length>0&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[(0,a.jsx)(S.A,{className:"w-6 h-6 text-gray-600"}),(0,a.jsx)("h4",{className:"font-bold text-gray-800 text-xl",children:"Generated Videos"}),(0,a.jsxs)(g,{variant:"secondary",className:"bg-green-100 text-green-800",children:[H.length," videos processed"]})]}),(0,a.jsx)("div",{className:"grid gap-4",children:H.map((e,t)=>(0,a.jsx)(c,{className:"hover:shadow-lg transition-all duration-200 border-l-4 border-l-blue-500",children:(0,a.jsx)(m,{className:"p-6",children:(0,a.jsx)("div",{className:"flex items-start justify-between",children:(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:["success"===e.generation_status?(0,a.jsx)("div",{className:"w-5 h-5 bg-green-500 rounded-full mr-4 flex items-center justify-center",children:(0,a.jsx)(k.A,{className:"w-3 h-3 text-white"})}):"skipped_empty_script"===e.generation_status?(0,a.jsx)("div",{className:"w-5 h-5 bg-yellow-500 rounded-full mr-4 flex items-center justify-center",children:(0,a.jsx)(z.A,{className:"w-3 h-3 text-white"})}):(0,a.jsx)("div",{className:"w-5 h-5 bg-red-500 rounded-full mr-4"}),(0,a.jsx)("h5",{className:"font-bold text-gray-800 text-xl",children:e.title||"Chapter ".concat(e.page)})]}),(0,a.jsxs)(g,{variant:"outline",className:"mb-4",children:["Page ",e.page]}),"success"===e.generation_status&&(0,a.jsx)(g,{className:"bg-green-100 text-green-800 hover:bg-green-100",children:"✅ Generated Successfully"}),"skipped_empty_script"===e.generation_status&&(0,a.jsx)(g,{className:"bg-yellow-100 text-yellow-800 hover:bg-yellow-100",children:"⚠️ Skipped (Empty Script)"}),"failed"===e.generation_status&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(g,{className:"bg-red-100 text-red-800 hover:bg-red-100",children:"❌ Generation Failed"}),(0,a.jsx)("p",{className:"text-sm text-red-600 mt-2 p-3 bg-red-50 rounded-lg border border-red-200",children:e.error_details||"Unknown error occurred during generation"})]})]})})})},t))})]})]})]}),(0,a.jsx)(F,{open:Y,onOpenChange:Z,children:(0,a.jsxs)(L,{className:"max-w-lg w-full mx-4 max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)(E,{className:"text-center",children:[(0,a.jsx)("div",{className:"w-20 h-20 bg-gradient-to-br from-amber-400 to-orange-500 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg",children:(0,a.jsx)(j.A,{className:"w-10 h-10 text-white"})}),(0,a.jsx)(q,{className:"text-2xl font-bold text-gray-800 mb-4",children:"Generate All Videos?"}),(0,a.jsx)(I,{asChild:!0,children:(0,a.jsxs)("p",{className:"text-gray-600 mb-6 text-lg leading-relaxed",children:["This will generate videos for all pages in the selected course content.",(0,a.jsx)("br",{}),"This process may take several minutes to complete."]})})]}),(0,a.jsx)(c,{className:"bg-gradient-to-r from-amber-50 to-orange-50 border-2 border-amber-200 mb-6",children:(0,a.jsxs)(m,{className:"p-6",children:[(0,a.jsx)("div",{className:"text-center font-bold mb-4 text-amber-900 text-lg",children:"Selected Configuration"}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,a.jsx)(N.A,{className:"w-6 h-6 text-amber-600"}),(0,a.jsx)("p",{className:"font-medium text-amber-800",children:"Avatar"}),(0,a.jsx)("p",{className:"text-sm text-amber-700 break-words",children:null==(e=o.find(e=>e.id===h))?void 0:e.avatar_name})]})}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,a.jsx)(w.A,{className:"w-6 h-6 text-amber-600"}),(0,a.jsx)("p",{className:"font-medium text-amber-800",children:"Course"}),(0,a.jsx)("p",{className:"text-sm text-amber-700 break-words",children:null==(t=et.find(e=>e.course_id===Number(G)))?void 0:t.course_title})]})}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,a.jsx)(C.A,{className:"w-6 h-6 text-amber-600"}),(0,a.jsx)("p",{className:"font-medium text-amber-800",children:"Version"}),(0,a.jsx)("p",{className:"text-sm text-amber-700 break-words",children:null==(s=es.find(e=>e.version_id===D))?void 0:s.version_number})]})})]})]})}),(0,a.jsxs)($,{className:"flex space-x-4",children:[(0,a.jsx)(r.$,{variant:"outline",className:"flex-1 h-12 text-base",onClick:()=>Z(!1),children:"Cancel"}),(0,a.jsx)(r.$,{variant:"brand",className:"flex-1 h-12 text-base",onClick:ea,children:"Yes, Generate Videos"})]})]})})]})}function O(){return(0,a.jsx)(J,{})}},5057:(e,t,s)=>{"use strict";s.d(t,{J:()=>l});var a=s(5155);s(2115);var r=s(968),n=s(9434);function l(e){let{className:t,...s}=e;return(0,a.jsx)(r.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...s})}},9409:(e,t,s)=>{"use strict";s.d(t,{bq:()=>x,eb:()=>u,gC:()=>m,l6:()=>c,yv:()=>d});var a=s(5155);s(2115);var r=s(9963),n=s(6474),l=s(5196),i=s(7863),o=s(9434);function c(e){let{...t}=e;return(0,a.jsx)(r.bL,{"data-slot":"select",...t})}function d(e){let{...t}=e;return(0,a.jsx)(r.WT,{"data-slot":"select-value",...t})}function x(e){let{className:t,size:s="default",children:l,...i}=e;return(0,a.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":s,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...i,children:[l,(0,a.jsx)(r.In,{asChild:!0,children:(0,a.jsx)(n.A,{className:"size-4 opacity-50"})})]})}function m(e){let{className:t,children:s,position:n="popper",...l}=e;return(0,a.jsx)(r.ZL,{children:(0,a.jsxs)(r.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:n,...l,children:[(0,a.jsx)(h,{}),(0,a.jsx)(r.LM,{className:(0,o.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:s}),(0,a.jsx)(g,{})]})})}function u(e){let{className:t,children:s,...n}=e;return(0,a.jsxs)(r.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...n,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{children:(0,a.jsx)(l.A,{className:"size-4"})})}),(0,a.jsx)(r.p4,{children:s})]})}function h(e){let{className:t,...s}=e;return(0,a.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...s,children:(0,a.jsx)(i.A,{className:"size-4"})})}function g(e){let{className:t,...s}=e;return(0,a.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...s,children:(0,a.jsx)(n.A,{className:"size-4"})})}},9434:(e,t,s)=>{"use strict";s.d(t,{cn:()=>n});var a=s(2596),r=s(9688);function n(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,a.$)(t))}},9572:(e,t,s)=>{Promise.resolve().then(s.bind(s,602))}},e=>{var t=t=>e(e.s=t);e.O(0,[352,578,535,441,684,358],()=>t(9572)),_N_E=e.O()}]);