(()=>{var e={};e.id=24,e.ids=[24],e.modules={440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(1658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1652:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var r=s(687),a=s(9190),n=s(3210),i=s(5885),l=s(7e3);let c=({certificate:e})=>(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-4 flex items-center space-x-4 border border-gray-200",children:[(0,r.jsx)("div",{className:"flex-shrink-0 bg-purple-50 text-purple-700 w-12 h-12 rounded-full flex items-center justify-center text-xl",children:(0,r.jsx)(i.g,{icon:l.LPI})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h3",{className:"text-base font-semibold text-gray-800",children:e.title}),(0,r.jsxs)("p",{className:"text-gray-600 text-xs mt-1",children:["Domain: ",e.domain," • Level: ",e.level]}),(0,r.jsxs)("p",{className:"text-gray-600 text-xs",children:["Certificate ID: ",e.certificate_id]}),(0,r.jsxs)("p",{className:"text-gray-600 text-xs",children:["Issued: ",new Date(e.issued_date).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})]}),(0,r.jsxs)("p",{className:"text-gray-600 text-xs",children:[e.estimated_hours," hours • ",e.total_modules," modules completed"]})]}),(0,r.jsxs)("div",{className:"flex flex-col items-end space-y-1.5",children:[(0,r.jsx)("span",{className:"inline-block bg-purple-100 text-purple-700 text-xs px-2 py-0.5 rounded-full font-medium",children:"Verified"}),(0,r.jsxs)("div",{className:"flex items-center space-x-1.5",children:[(0,r.jsxs)("button",{className:"bg-gray-100 text-gray-800 px-3 py-1 rounded-md hover:bg-gray-200 transition-colors duration-200 text-xs flex items-center space-x-1",children:[(0,r.jsx)(i.g,{icon:l.pS3}),(0,r.jsx)("span",{children:"View"})]}),(0,r.jsxs)("button",{className:"bg-orange-500 text-white px-3 py-1 rounded-md hover:bg-orange-600 transition-colors duration-200 text-xs flex items-center space-x-1",children:[(0,r.jsx)(i.g,{icon:l.cbP}),(0,r.jsx)("span",{children:"Download"})]}),(0,r.jsxs)("button",{className:"bg-gray-100 text-gray-800 px-3 py-1 rounded-md hover:bg-gray-200 transition-colors duration-200 text-xs flex items-center space-x-1",children:[(0,r.jsx)(i.g,{icon:l.QOt}),(0,r.jsx)("span",{children:"Share"})]})]})]})]}),o=({value:e,label:t})=>(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-4 text-center border border-orange-200",children:[(0,r.jsx)("p",{className:"text-3xl font-bold text-orange-500",children:e}),(0,r.jsx)("p",{className:"text-gray-800 text-sm mt-1",children:t})]});function d(){let[e,t]=(0,n.useState)(null),[s,d]=(0,n.useState)(!0),[x,m]=(0,n.useState)(null),[u,p]=(0,n.useState)(null);if(u?.id,s)return(0,r.jsxs)("div",{className:"flex flex-col min-h-screen bg-[#fafbfc]",children:[(0,r.jsx)(a.default,{}),(0,r.jsx)("main",{className:"flex-grow container mx-auto px-6 py-10",children:(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"text-lg text-gray-600",children:"Loading certificates..."})})})]});if(x||!e)return(0,r.jsxs)("div",{className:"flex flex-col min-h-screen bg-[#fafbfc]",children:[(0,r.jsx)(a.default,{}),(0,r.jsx)("main",{className:"flex-grow container mx-auto px-6 py-10",children:(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"text-lg text-red-600",children:x||"Failed to load certificates"})})})]});let{certificates:h,statistics:f}=e;return(0,r.jsxs)("div",{className:"flex flex-col min-h-screen bg-[#fafbfc]",children:[(0,r.jsx)(a.default,{}),(0,r.jsxs)("main",{className:"flex-grow container mx-auto px-6 py-10",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-800 mb-2",children:"My Certificates"}),(0,r.jsx)("p",{className:"text-gray-600 text-base mb-6",children:"Download and share your course completion certificates"}),h.length>0?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"space-y-6 mb-8",children:h.map(e=>(0,r.jsx)(c,{certificate:e},e.course_id))}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,r.jsx)(o,{value:f.total_certificates,label:"Certificates Earned"}),(0,r.jsx)(o,{value:f.unique_domains,label:"Different Domains"}),(0,r.jsx)(o,{value:f.total_learning_hours,label:"Learning Hours"})]})]}):(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center",children:[(0,r.jsx)(i.g,{icon:l.LPI,className:"text-gray-400 text-4xl mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No certificates earned yet"}),(0,r.jsx)("p",{className:"text-gray-500 mb-4",children:"Complete courses to earn certificates"}),(0,r.jsx)("a",{href:"/courses",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700 transition-colors",children:"Browse Courses"})]})]})]})}},1820:e=>{"use strict";e.exports=require("os")},2053:(e,t,s)=>{Promise.resolve().then(s.bind(s,2714))},2714:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\AI-Trainer3\\\\frontend\\\\src\\\\app\\\\my-certificates\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\AI-Trainer3\\frontend\\src\\app\\my-certificates\\page.tsx","default")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},3997:e=>{"use strict";e.exports=require("tty")},4075:e=>{"use strict";e.exports=require("zlib")},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},5973:(e,t,s)=>{Promise.resolve().then(s.bind(s,1652))},6781:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>d,routeModule:()=>m,tree:()=>o});var r=s(5239),a=s(8088),n=s(8170),i=s.n(n),l=s(893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);s.d(t,c);let o={children:["",{children:["my-certificates",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,2714)),"C:\\Projects\\AI-Trainer3\\frontend\\src\\app\\my-certificates\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,8429)),"C:\\Projects\\AI-Trainer3\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Projects\\AI-Trainer3\\frontend\\src\\app\\my-certificates\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/my-certificates/page",pathname:"/my-certificates",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},7910:e=>{"use strict";e.exports=require("stream")},8354:e=>{"use strict";e.exports=require("util")},9021:e=>{"use strict";e.exports=require("fs")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9190:(e,t,s)=>{"use strict";s.d(t,{default:()=>o});var r=s(687),a=s(3210),n=s(5885),i=s(7e3),l=s(7346),c=s(7351);let o=()=>{let[e,t]=(0,a.useState)(!1),[s,o]=(0,a.useState)(null),d=(0,a.useRef)(null),x=e=>{try{let t=e.split(".")[1].replace(/-/g,"+").replace(/_/g,"/"),s=decodeURIComponent(atob(t).split("").map(function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)}).join(""));return JSON.parse(s)}catch(e){return console.error("Error decoding JWT:",e),null}};(0,a.useEffect)(()=>{let e=localStorage.getItem("token");if(e){let t=x(e);t&&o({id:t.id,email:t.email,username:t.username,role:t.role,first_name:t.first_name||t.username,last_name:t.last_name||""})}},[]);let m=(e,t)=>(e?e.charAt(0).toUpperCase():"")+(t?t.charAt(0).toUpperCase():"")||"U";return(0,a.useEffect)(()=>{let e=e=>{d.current&&!d.current.contains(e.target)&&t(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]),(0,r.jsxs)("nav",{className:"bg-white p-4 shadow-md",children:[(0,r.jsxs)("div",{className:"container mx-auto flex justify-between items-center",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"bg-orange-500 rounded-xl p-2 flex items-center justify-center rounded-lg",children:(0,r.jsx)(c.A,{className:"w-6 h-6 text-white"})}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"text-orange-500 text-lg font-bold",children:"EX"}),(0,r.jsx)("span",{className:"text-[#005071] text-lg font-bold",children:"Learn"})]})]}),(0,r.jsx)("div",{className:"flex-grow flex justify-center mx-4",children:(0,r.jsx)("div",{className:"relative w-full max-w-[280px]",children:(0,r.jsx)("input",{type:"text",placeholder:"Search courses, topics, or instructors...",className:"w-full pl-3 pr-3 py-1 rounded-full border border-transparent focus:outline-none focus:ring-1 focus:ring-orange-100 text-xs placeholder-gray-400"})})}),(0,r.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,r.jsx)(l.A,{}),(0,r.jsx)("div",{className:"relative",ref:d,children:s?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 cursor-pointer hover:bg-gray-50 rounded-lg px-2 py-1 transition-colors duration-200",onClick:()=>{t(!e)},children:[(0,r.jsx)("div",{className:"bg-orange-500 text-white rounded-full h-8 w-8 flex items-center justify-center font-medium",children:m(s.first_name,s.last_name)}),(0,r.jsx)("span",{className:"text-gray-700 text-sm font-medium",children:s.first_name}),(0,r.jsx)(n.g,{icon:i.Jt$,className:`text-gray-500 text-xs transition-transform duration-200 ${e?"rotate-180":""}`})]}),e&&(0,r.jsxs)("div",{className:"absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50",children:[(0,r.jsx)("div",{className:"px-4 py-3 border-b border-gray-100",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"bg-orange-500 text-white rounded-full h-10 w-10 flex items-center justify-center font-medium",children:m(s.first_name,s.last_name)}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{className:"text-sm font-medium text-gray-900",children:[s.first_name," ",s.last_name]}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:s.email})]})]})}),(0,r.jsxs)("div",{className:"py-1",children:[(0,r.jsxs)("button",{className:"w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200",onClick:()=>{t(!1)},children:[(0,r.jsx)(n.g,{icon:i.X46,className:"mr-3 text-gray-400"}),"Profile"]}),(0,r.jsxs)("button",{className:"w-full flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors duration-200",onClick:()=>{localStorage.removeItem("token"),sessionStorage.removeItem("token"),localStorage.removeItem("authToken"),sessionStorage.removeItem("authToken"),o(null),window.location.href="/signin"},children:[(0,r.jsx)(n.g,{icon:i.GxD,className:"mr-3 text-red-500"}),"Log out"]})]})]})]}):(0,r.jsxs)("a",{href:"/signin",className:"flex items-center space-x-2 bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors duration-200",children:[(0,r.jsx)(n.g,{icon:i.X46,className:"text-sm"}),(0,r.jsx)("span",{className:"text-sm font-medium",children:"Sign In"})]})})]})]}),(0,r.jsx)("div",{className:"container mx-auto mt-4",children:(0,r.jsxs)("ul",{className:"flex space-x-8",children:[(0,r.jsx)("li",{children:(0,r.jsxs)("a",{href:"/",className:"flex items-center space-x-2 text-orange-500 border-b-2 border-orange-500 pb-2",children:[(0,r.jsx)(n.g,{icon:i.v02,className:"text-sm"}),(0,r.jsx)("span",{children:"Home"})]})}),(0,r.jsx)("li",{children:(0,r.jsxs)("a",{href:"/my-dashboard",className:"flex items-center space-x-2 text-gray-700 hover:text-orange-500",children:[(0,r.jsx)(n.g,{icon:i.xiI,className:"text-sm"}),(0,r.jsx)("span",{children:"My Dashboard"})]})}),(0,r.jsx)("li",{children:(0,r.jsxs)("a",{href:"/my-learning",className:"flex items-center space-x-2 text-gray-700 hover:text-orange-500",children:[(0,r.jsx)(n.g,{icon:i.ReK,className:"text-sm"}),(0,r.jsx)("span",{children:"My Learning"})]})}),(0,r.jsx)("li",{children:(0,r.jsxs)("a",{href:"/my-certificates",className:"flex items-center space-x-2 text-gray-700 hover:text-orange-500",children:[(0,r.jsx)(n.g,{icon:i.fmL,className:"text-sm"}),(0,r.jsx)("span",{children:"Certificates"})]})})]})})]})}},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9551:e=>{"use strict";e.exports=require("url")},9646:e=>{"use strict";e.exports=require("child_process")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,773,658,578],()=>s(6781));module.exports=r})();